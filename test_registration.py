#!/usr/bin/env python3
"""
Quick test script to verify registration API
"""

import requests
import json

def test_registration():
    url = "http://127.0.0.1:8000/api/v1/auth/register/"
    
    # Test data
    test_data = {
        "username": "testuser123",
        "email": "<EMAIL>",
        "password": "1234567890",  # This should fail with numeric password
        "password_confirm": "1234567890"
    }
    
    print("Testing registration with numeric password (should fail)...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, headers={
            'Content-Type': 'application/json'
        })
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    # Test with mixed password (should succeed)
    print("\n" + "="*50)
    print("Testing registration with mixed password (should succeed)...")
    
    test_data2 = {
        "username": "testuser456",
        "email": "<EMAIL>", 
        "password": "password123",  # Mixed password
        "password_confirm": "password123"
    }
    
    print(f"Data: {json.dumps(test_data2, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data2, headers={
            'Content-Type': 'application/json'
        })
        
        print(f"\nResponse Status: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_registration()
