# YourWorkspace 测试系统

这是YourWorkspace项目的综合测试系统，包含后端API测试和集成测试。

## 测试结构

```
test/
├── README.md                 # 测试文档
├── requirements.txt          # Python测试依赖
├── package.json             # Node.js测试依赖
├── config/                  # 测试配置
│   ├── test_config.py       # Python测试配置
│   └── test_config.js       # JavaScript测试配置
├── backend/                 # 后端API测试
│   ├── test_auth.py         # 认证API测试
│   ├── test_todo.py         # Todo API测试
│   ├── test_achievements.py # 成就API测试
│   ├── test_plans.py        # 计划API测试
│   └── test_anchor.py       # 用户档案API测试
├── integration/             # 集成测试
│   ├── test_app_startup.py  # 应用启动测试
│   ├── test_frontend.js     # 前端集成测试
│   └── test_e2e.js          # 端到端测试
├── utils/                   # 测试工具
│   ├── test_helpers.py      # Python测试辅助函数
│   ├── test_helpers.js      # JavaScript测试辅助函数
│   └── fixtures.py          # 测试数据
└── scripts/                 # 测试脚本
    ├── run_all_tests.py     # 运行所有测试
    ├── setup_test_env.py    # 设置测试环境
    └── cleanup_test_env.py  # 清理测试环境

## 快速开始

### 安装依赖

```bash
# Python依赖
pip install -r requirements.txt

# Node.js依赖
npm install
```

### 运行测试

```bash
# 运行所有测试
python scripts/run_all_tests.py

# 只运行后端API测试
python -m pytest backend/ -v

# 只运行集成测试
python -m pytest integration/ -v

# 运行前端测试
npm test
```

## 测试类型

### 1. 后端API测试
- 认证系统测试（注册、登录、令牌刷新）
- CRUD操作测试（Todo、成就、计划）
- 权限验证测试
- 错误处理测试

### 2. 集成测试
- 应用启动测试
- 前后端通信测试
- 数据库连接测试
- 路由测试

### 3. 端到端测试
- 用户注册流程
- 登录流程
- 页面导航测试
- 功能完整性测试

## 测试配置

测试使用独立的测试数据库和配置，不会影响开发或生产环境。

### 环境变量

创建 `.env.test` 文件：

```bash
# 测试环境配置
FLASK_CONFIG=testing
SECRET_KEY=test_secret_key
JWT_SECRET_KEY=test_jwt_secret_key
TEST_DATABASE_URL=sqlite:///test.db

# 前端测试配置
VITE_API_BASE_URL=http://localhost:5000/api/v1
TEST_FRONTEND_URL=http://localhost:5173
```

## 持续集成

测试系统支持CI/CD集成，可以在GitHub Actions、GitLab CI等平台上运行。

## 故障排除

### 常见问题

1. **测试数据库连接失败**
   - 检查数据库配置
   - 确保测试数据库存在

2. **API测试失败**
   - 确保后端服务正在运行
   - 检查API端点配置

3. **前端测试失败**
   - 确保前端开发服务器正在运行
   - 检查浏览器驱动程序

## 贡献指南

1. 添加新测试时，请遵循现有的命名约定
2. 确保测试具有良好的文档和注释
3. 运行所有测试确保没有回归
4. 更新相关文档
