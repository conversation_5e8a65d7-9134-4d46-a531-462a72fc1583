# 测试框架
pytest==7.4.3
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-asyncio==0.21.1

# HTTP请求测试
requests==2.31.0
httpx==0.25.2

# 数据库测试
pytest-flask==1.3.0
flask-testing==0.8.1

# 测试数据生成
faker==20.1.0
factory-boy==3.3.0

# 测试报告
pytest-html==4.1.1
pytest-json-report==1.5.0

# 代码覆盖率
coverage==7.3.2

# 环境管理
python-dotenv==1.0.0

# 时间处理
freezegun==1.2.2

# JSON处理
jsonschema==4.20.0

# 并发测试
pytest-xdist==3.5.0
