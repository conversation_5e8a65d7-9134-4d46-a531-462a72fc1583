#!/usr/bin/env python3
"""
简化的测试脚本，用于验证应用基本功能
不需要额外依赖，使用Python标准库
"""

import os
import sys
import time
import json
import subprocess
import urllib.request
import urllib.parse
import urllib.error
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

# 测试配置
API_BASE_URL = 'http://127.0.0.1:5000'
FRONTEND_URL = 'http://localhost:5173'
STARTUP_TIMEOUT = 60


class SimpleTestRunner:
    """简单测试运行器"""
    
    def __init__(self):
        self.processes = []
        self.test_results = {}
    
    def log(self, message, level='INFO'):
        """日志输出"""
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
    
    def start_backend(self):
        """启动后端服务"""
        self.log("Starting backend service...")
        
        backend_dir = PROJECT_ROOT / 'backend'
        if not backend_dir.exists():
            self.log("Backend directory not found", 'ERROR')
            return None
        
        # 设置环境变量
        env = os.environ.copy()
        env.update({
            'FLASK_CONFIG': 'development',
            'SECRET_KEY': 'test-secret-key',
            'JWT_SECRET_KEY': 'test-jwt-secret-key'
        })
        
        try:
            # 尝试使用python3启动
            process = subprocess.Popen(
                [sys.executable, 'run.py'],
                cwd=backend_dir,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.processes.append(('backend', process))
            self.log("Backend process started")
            return process
            
        except Exception as e:
            self.log(f"Failed to start backend: {e}", 'ERROR')
            return None
    
    def wait_for_service(self, url, timeout=60):
        """等待服务启动"""
        self.log(f"Waiting for service at {url}...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = urllib.request.urlopen(url, timeout=5)
                if response.getcode() < 500:
                    self.log(f"Service at {url} is ready")
                    return True
            except (urllib.error.URLError, urllib.error.HTTPError, OSError):
                pass
            time.sleep(1)
        
        self.log(f"Service at {url} failed to start within {timeout}s", 'ERROR')
        return False
    
    def test_backend_startup(self):
        """测试后端启动"""
        self.log("Testing backend startup...")
        
        # 启动后端
        backend_process = self.start_backend()
        if not backend_process:
            self.test_results['backend_startup'] = False
            return False
        
        # 等待服务启动
        if not self.wait_for_service(API_BASE_URL, STARTUP_TIMEOUT):
            self.test_results['backend_startup'] = False
            return False
        
        # 验证进程仍在运行
        if backend_process.poll() is not None:
            self.log("Backend process terminated unexpectedly", 'ERROR')
            self.test_results['backend_startup'] = False
            return False
        
        self.log("Backend startup test passed", 'SUCCESS')
        self.test_results['backend_startup'] = True
        return True
    
    def test_api_endpoints(self):
        """测试API端点"""
        self.log("Testing API endpoints...")
        
        endpoints_to_test = [
            ('/', 'GET'),
            ('/api/v1/auth/register', 'POST'),
            ('/api/v1/todo/todos', 'GET'),
            ('/api/v1/achievements/', 'GET'),
            ('/api/v1/plans/', 'GET'),
        ]
        
        all_passed = True
        
        for endpoint, method in endpoints_to_test:
            url = f"{API_BASE_URL}{endpoint}"
            
            try:
                if method == 'GET':
                    response = urllib.request.urlopen(url, timeout=10)
                elif method == 'POST':
                    # 对于POST请求，发送空数据
                    data = json.dumps({}).encode('utf-8')
                    req = urllib.request.Request(url, data=data, headers={'Content-Type': 'application/json'})
                    req.get_method = lambda: 'POST'
                    response = urllib.request.urlopen(req, timeout=10)
                
                status_code = response.getcode()
                
                # 检查不是服务器错误
                if status_code >= 500:
                    self.log(f"Server error on {endpoint}: {status_code}", 'ERROR')
                    all_passed = False
                else:
                    self.log(f"Endpoint {endpoint} responded with {status_code}")
                
            except urllib.error.HTTPError as e:
                # HTTP错误是可以接受的（如401未授权）
                if e.code >= 500:
                    self.log(f"Server error on {endpoint}: {e.code}", 'ERROR')
                    all_passed = False
                else:
                    self.log(f"Endpoint {endpoint} responded with {e.code}")
                    
            except Exception as e:
                self.log(f"Failed to connect to {endpoint}: {e}", 'ERROR')
                all_passed = False
        
        self.test_results['api_endpoints'] = all_passed
        return all_passed
    
    def test_user_registration_and_login(self):
        """测试用户注册和登录"""
        self.log("Testing user registration and login...")
        
        # 测试用户数据
        test_user = {
            'username': f'testuser_{int(time.time())}',
            'email': f'test_{int(time.time())}@example.com',
            'password': 'TestPassword123!'
        }
        
        try:
            # 测试注册
            register_url = f"{API_BASE_URL}/api/v1/auth/register"
            register_data = json.dumps(test_user).encode('utf-8')
            register_req = urllib.request.Request(
                register_url, 
                data=register_data, 
                headers={'Content-Type': 'application/json'}
            )
            register_req.get_method = lambda: 'POST'
            
            register_response = urllib.request.urlopen(register_req, timeout=10)
            register_status = register_response.getcode()
            
            if register_status not in [200, 201]:
                self.log(f"Registration failed with status {register_status}", 'ERROR')
                self.test_results['user_auth'] = False
                return False
            
            self.log("User registration successful")
            
            # 测试登录
            login_url = f"{API_BASE_URL}/api/v1/auth/login"
            login_data = json.dumps({
                'email': test_user['email'],
                'password': test_user['password']
            }).encode('utf-8')
            login_req = urllib.request.Request(
                login_url, 
                data=login_data, 
                headers={'Content-Type': 'application/json'}
            )
            login_req.get_method = lambda: 'POST'
            
            login_response = urllib.request.urlopen(login_req, timeout=10)
            login_status = login_response.getcode()
            
            if login_status not in [200, 201]:
                self.log(f"Login failed with status {login_status}", 'ERROR')
                self.test_results['user_auth'] = False
                return False
            
            # 检查响应包含令牌
            login_data = json.loads(login_response.read().decode('utf-8'))
            if 'access_token' not in login_data:
                self.log("Login response missing access_token", 'ERROR')
                self.test_results['user_auth'] = False
                return False
            
            self.log("User login successful", 'SUCCESS')
            self.test_results['user_auth'] = True
            return True
            
        except Exception as e:
            self.log(f"User authentication test failed: {e}", 'ERROR')
            self.test_results['user_auth'] = False
            return False
    
    def test_frontend_availability(self):
        """测试前端可用性（如果可能）"""
        self.log("Testing frontend availability...")
        
        # 检查npm是否可用
        try:
            subprocess.run(['npm', '--version'], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("npm not available, skipping frontend test", 'WARNING')
            self.test_results['frontend'] = True  # 跳过不算失败
            return True
        
        # 检查前端目录
        frontend_dir = PROJECT_ROOT / 'svelte@latest'
        if not frontend_dir.exists() or not (frontend_dir / 'package.json').exists():
            self.log("Frontend directory not found, skipping frontend test", 'WARNING')
            self.test_results['frontend'] = True  # 跳过不算失败
            return True
        
        # 尝试访问前端（假设已经在运行）
        try:
            response = urllib.request.urlopen(FRONTEND_URL, timeout=5)
            if response.getcode() == 200:
                self.log("Frontend is accessible", 'SUCCESS')
                self.test_results['frontend'] = True
                return True
        except:
            pass
        
        self.log("Frontend not accessible (may not be running)", 'WARNING')
        self.test_results['frontend'] = True  # 不强制要求前端运行
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log("Starting comprehensive test suite...")
        
        all_passed = True
        
        # 测试后端启动
        if not self.test_backend_startup():
            all_passed = False
        
        # 如果后端启动成功，继续其他测试
        if self.test_results.get('backend_startup', False):
            if not self.test_api_endpoints():
                all_passed = False
            
            if not self.test_user_registration_and_login():
                all_passed = False
        
        # 测试前端（可选）
        self.test_frontend_availability()
        
        return all_passed
    
    def print_summary(self):
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print("📊 TEST SUMMARY")
        print("="*60)
        
        for test_name, passed in self.test_results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"{test_name.upper():<20} {status}")
        
        all_passed = all(self.test_results.values())
        
        if all_passed:
            print("\n🎉 All tests passed!")
        else:
            print("\n💥 Some tests failed!")
        
        print("="*60)
        
        return all_passed
    
    def cleanup(self):
        """清理进程"""
        self.log("Cleaning up processes...")
        
        for name, process in self.processes:
            if process and process.poll() is None:
                self.log(f"Terminating {name} process...")
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    self.log(f"Force killing {name} process...")
                    process.kill()
        
        self.log("Cleanup complete")


def main():
    """主函数"""
    runner = SimpleTestRunner()
    
    try:
        success = runner.run_all_tests()
        runner.print_summary()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        runner.log("Test interrupted by user", 'WARNING')
        sys.exit(1)
    except Exception as e:
        runner.log(f"Unexpected error: {e}", 'ERROR')
        sys.exit(1)
    finally:
        runner.cleanup()


if __name__ == '__main__':
    main()
