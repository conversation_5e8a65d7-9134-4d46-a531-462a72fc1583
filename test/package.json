{"name": "yourworkspace-tests", "version": "1.0.0", "description": "Test suite for YourWorkspace application", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:frontend": "jest --testPathPattern=integration/test_frontend", "install:browsers": "npx playwright install"}, "devDependencies": {"@playwright/test": "^1.40.0", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "axios": "^1.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "puppeteer": "^21.5.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/integration", "<rootDir>/utils"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts", "**/?(*.)+(spec|test).js"], "collectCoverageFrom": ["**/*.{ts,js}", "!**/*.d.ts", "!**/node_modules/**"]}, "keywords": ["test", "api", "integration", "e2e"], "author": "YourWorkspace Team", "license": "MIT"}