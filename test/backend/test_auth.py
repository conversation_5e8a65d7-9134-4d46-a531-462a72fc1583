"""
认证API测试
测试用户注册、登录、登出、令牌刷新等功能
"""

import pytest
import json
from test.utils.test_helpers import APIClient, TestDataManager, assert_response_success, assert_response_error, assert_json_structure
from test.config.test_config import TestConfig, API_ENDPOINTS


class TestAuthAPI:
    """认证API测试类"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.api_client = APIClient()
        self.data_manager = TestDataManager(self.api_client)
        yield
        # 清理
        self.data_manager.cleanup()
    
    def test_user_registration_success(self):
        """测试用户注册成功"""
        user_data = {
            'username': 'newuser123',
            'email': '<EMAIL>',
            'password': 'SecurePassword123!'
        }
        
        response = self.api_client.register_user(user_data)
        assert_response_success(response, 201)
        
        data = response.json()
        assert_json_structure(data, ['message', 'user'])
        assert data['user']['username'] == user_data['username']
        assert data['user']['email'] == user_data['email']
        assert 'password' not in data['user']  # 密码不应该返回
    
    def test_user_registration_duplicate_username(self):
        """测试重复用户名注册失败"""
        user_data = {
            'username': 'duplicateuser',
            'email': '<EMAIL>',
            'password': 'SecurePassword123!'
        }
        
        # 第一次注册
        response1 = self.api_client.register_user(user_data)
        assert_response_success(response1, 201)
        
        # 第二次注册相同用户名
        user_data['email'] = '<EMAIL>'  # 不同邮箱
        response2 = self.api_client.register_user(user_data)
        assert_response_error(response2, 400)
    
    def test_user_registration_duplicate_email(self):
        """测试重复邮箱注册失败"""
        user_data = {
            'username': 'user1',
            'email': '<EMAIL>',
            'password': 'SecurePassword123!'
        }
        
        # 第一次注册
        response1 = self.api_client.register_user(user_data)
        assert_response_success(response1, 201)
        
        # 第二次注册相同邮箱
        user_data['username'] = 'user2'  # 不同用户名
        response2 = self.api_client.register_user(user_data)
        assert_response_error(response2, 400)
    
    def test_user_registration_invalid_data(self):
        """测试无效数据注册失败"""
        # 测试缺少必需字段
        invalid_data_sets = [
            {},  # 空数据
            {'username': 'test'},  # 缺少邮箱和密码
            {'email': '<EMAIL>'},  # 缺少用户名和密码
            {'password': 'password'},  # 缺少用户名和邮箱
            {'username': '', 'email': '<EMAIL>', 'password': 'password'},  # 空用户名
            {'username': 'test', 'email': '', 'password': 'password'},  # 空邮箱
            {'username': 'test', 'email': '<EMAIL>', 'password': ''},  # 空密码
            {'username': 'test', 'email': 'invalid-email', 'password': 'password'},  # 无效邮箱
            {'username': 'test', 'email': '<EMAIL>', 'password': '123'},  # 密码太短
        ]
        
        for invalid_data in invalid_data_sets:
            response = self.api_client.register_user(invalid_data)
            assert response.status_code == 400, f"Expected 400 for data: {invalid_data}"
    
    def test_user_login_success(self):
        """测试用户登录成功"""
        # 先注册用户
        user_data = self.data_manager.create_test_user()
        assert user_data is not None
        
        # 登录
        credentials = {
            'email': user_data['email'],
            'password': user_data['password']
        }
        
        response = self.api_client.login_user(credentials)
        assert_response_success(response, 200)
        
        data = response.json()
        assert_json_structure(data, ['access_token', 'refresh_token', 'user'])
        assert data['user']['email'] == credentials['email']
        assert 'password' not in data['user']
    
    def test_user_login_invalid_credentials(self):
        """测试无效凭据登录失败"""
        # 先注册用户
        user_data = self.data_manager.create_test_user()
        assert user_data is not None
        
        # 测试错误密码
        credentials = {
            'email': user_data['email'],
            'password': 'wrongpassword'
        }
        
        response = self.api_client.login_user(credentials)
        assert_response_error(response, 401)
        
        # 测试不存在的邮箱
        credentials = {
            'email': '<EMAIL>',
            'password': 'password'
        }
        
        response = self.api_client.login_user(credentials)
        assert_response_error(response, 401)
    
    def test_user_login_missing_data(self):
        """测试缺少数据登录失败"""
        invalid_credentials_sets = [
            {},  # 空数据
            {'email': '<EMAIL>'},  # 缺少密码
            {'password': 'password'},  # 缺少邮箱
            {'email': '', 'password': 'password'},  # 空邮箱
            {'email': '<EMAIL>', 'password': ''},  # 空密码
        ]
        
        for invalid_credentials in invalid_credentials_sets:
            response = self.api_client.login_user(invalid_credentials)
            assert response.status_code == 400, f"Expected 400 for credentials: {invalid_credentials}"
    
    def test_get_current_user(self):
        """测试获取当前用户信息"""
        # 先注册并登录用户
        user_data = self.data_manager.create_test_user()
        assert user_data is not None
        
        credentials = {
            'email': user_data['email'],
            'password': user_data['password']
        }
        
        login_response = self.api_client.login_user(credentials)
        assert_response_success(login_response, 200)
        
        # 获取当前用户信息
        response = self.api_client.get(API_ENDPOINTS['auth']['me'])
        assert_response_success(response, 200)
        
        data = response.json()
        assert_json_structure(data, ['id', 'username', 'email'])
        assert data['email'] == user_data['email']
        assert 'password' not in data
    
    def test_get_current_user_unauthorized(self):
        """测试未认证获取用户信息失败"""
        response = self.api_client.get(API_ENDPOINTS['auth']['me'])
        assert_response_error(response, 401)
    
    def test_user_logout(self):
        """测试用户登出"""
        # 先注册并登录用户
        user_data = self.data_manager.create_test_user()
        assert user_data is not None
        
        credentials = {
            'email': user_data['email'],
            'password': user_data['password']
        }
        
        login_response = self.api_client.login_user(credentials)
        assert_response_success(login_response, 200)
        
        # 登出
        logout_response = self.api_client.logout_user()
        assert logout_response.status_code in [200, 204]
        
        # 验证令牌已失效
        response = self.api_client.get(API_ENDPOINTS['auth']['me'])
        assert_response_error(response, 401)
    
    def test_change_password(self):
        """测试修改密码"""
        # 先注册并登录用户
        user_data = self.data_manager.create_test_user()
        assert user_data is not None
        
        credentials = {
            'email': user_data['email'],
            'password': user_data['password']
        }
        
        login_response = self.api_client.login_user(credentials)
        assert_response_success(login_response, 200)
        
        # 修改密码
        new_password = 'NewSecurePassword123!'
        change_data = {
            'current_password': user_data['password'],
            'new_password': new_password
        }
        
        response = self.api_client.post(API_ENDPOINTS['auth']['change_password'], json=change_data)
        assert_response_success(response, 200)
        
        # 验证新密码可以登录
        self.api_client.clear_auth()
        new_credentials = {
            'email': user_data['email'],
            'password': new_password
        }
        
        new_login_response = self.api_client.login_user(new_credentials)
        assert_response_success(new_login_response, 200)
    
    def test_change_password_wrong_current(self):
        """测试错误当前密码修改失败"""
        # 先注册并登录用户
        user_data = self.data_manager.create_test_user()
        assert user_data is not None
        
        credentials = {
            'email': user_data['email'],
            'password': user_data['password']
        }
        
        login_response = self.api_client.login_user(credentials)
        assert_response_success(login_response, 200)
        
        # 使用错误的当前密码
        change_data = {
            'current_password': 'wrongpassword',
            'new_password': 'NewSecurePassword123!'
        }
        
        response = self.api_client.post(API_ENDPOINTS['auth']['change_password'], json=change_data)
        assert_response_error(response, 400)
    
    def test_change_password_unauthorized(self):
        """测试未认证修改密码失败"""
        change_data = {
            'current_password': 'oldpassword',
            'new_password': 'newpassword'
        }
        
        response = self.api_client.post(API_ENDPOINTS['auth']['change_password'], json=change_data)
        assert_response_error(response, 401)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
