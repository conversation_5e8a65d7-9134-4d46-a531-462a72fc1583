"""
全面测试覆盖 - 确保Flask项目所有功能在重构前正常工作
测试用户工作流程、数据一致性、API集成等
"""

import pytest
import json
import datetime
from test.utils.test_helpers import APIClient, TestDataManager, assert_response_success, assert_response_error, assert_json_structure
from test.config.test_config import TestConfig, API_ENDPOINTS


class TestUserWorkflow:
    """测试完整用户工作流程"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.api_client = APIClient()
        self.data_manager = TestDataManager(self.api_client)
        yield
        # 清理
        self.data_manager.cleanup()
    
    def test_complete_user_journey(self):
        """测试从用户注册到使用所有功能的完整流程"""
        # 1. 用户注册
        user_data = {
            'username': 'journeyuser',
            'email': '<EMAIL>',
            'password': 'SecurePassword123!'
        }
        
        register_response = self.api_client.register_user(user_data)
        assert_response_success(register_response, 201)
        
        # 2. 用户登录
        credentials = {
            'email': user_data['email'],
            'password': user_data['password']
        }
        
        login_response = self.api_client.login_user(credentials)
        assert_response_success(login_response, 200)
        login_data = login_response.json()
        
        # 3. 更新用户档案
        profile_data = {
            'professional_title': 'Software Engineer',
            'one_liner_bio': 'Passionate about clean code',
            'skill': 'Python, Django, React',
            'summary': 'Experienced developer with 5+ years'
        }
        
        profile_response = self.api_client.put(API_ENDPOINTS['anchor']['profile'], json=profile_data)
        assert_response_success(profile_response, 200)
        
        # 4. 创建待办事项
        todo_data = {
            'title': 'Complete Django migration',
            'description': 'Migrate Flask app to Django',
            'priority': 'high',
            'due_date': (datetime.date.today() + datetime.timedelta(days=7)).isoformat()
        }
        
        todo_response = self.api_client.post(API_ENDPOINTS['todo']['todos'], json=todo_data)
        assert_response_success(todo_response, 201)
        todo_id = todo_response.json()['id']
        
        # 5. 创建成就
        achievement_data = {
            'title': 'Completed Flask Project',
            'description': 'Successfully built a task management system',
            'quantifiable_results': 'Delivered on time with 95% test coverage',
            'core_skills_json': ['Flask', 'SQLAlchemy', 'JWT'],
            'date_achieved': datetime.date.today().isoformat()
        }
        
        achievement_response = self.api_client.post(API_ENDPOINTS['achievements']['list'], json=achievement_data)
        assert_response_success(achievement_response, 201)
        
        # 6. 创建计划
        plan_data = {
            'goal_type': 'career',
            'title': 'Learn Django',
            'description': 'Master Django framework for web development',
            'target_date': (datetime.date.today() + datetime.timedelta(days=30)).isoformat(),
            'status': 'active'
        }
        
        plan_response = self.api_client.post(API_ENDPOINTS['plans']['list'], json=plan_data)
        assert_response_success(plan_response, 201)
        
        # 7. 验证数据一致性 - 获取所有数据
        profile_get_response = self.api_client.get(API_ENDPOINTS['anchor']['profile'])
        assert_response_success(profile_get_response, 200)
        
        todos_response = self.api_client.get(API_ENDPOINTS['todo']['todos'])
        assert_response_success(todos_response, 200)
        
        achievements_response = self.api_client.get(API_ENDPOINTS['achievements']['list'])
        assert_response_success(achievements_response, 200)
        
        plans_response = self.api_client.get(API_ENDPOINTS['plans']['list'])
        assert_response_success(plans_response, 200)
        
        # 验证数据完整性
        profile_result = profile_get_response.json()
        assert profile_result['professional_title'] == profile_data['professional_title']
        
        todos_result = todos_response.json()
        assert len(todos_result) >= 1
        assert any(todo['title'] == todo_data['title'] for todo in todos_result)
        
        achievements_result = achievements_response.json()
        assert len(achievements_result) >= 1
        assert any(ach['title'] == achievement_data['title'] for ach in achievements_result)
        
        plans_result = plans_response.json()
        assert len(plans_result) >= 1
        assert any(plan['title'] == plan_data['title'] for plan in plans_result)


class TestTodoItemWorkflow:
    """测试待办事项完整工作流程"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.api_client = APIClient()
        self.data_manager = TestDataManager(self.api_client)
        # 创建并登录测试用户
        self.user_data = self.data_manager.create_test_user()
        credentials = {
            'email': self.user_data['email'],
            'password': self.user_data['password']
        }
        login_response = self.api_client.login_user(credentials)
        assert_response_success(login_response, 200)
        yield
        self.data_manager.cleanup()
    
    def test_todo_crud_operations(self):
        """测试待办事项的完整CRUD操作"""
        # Create
        todo_data = {
            'title': 'Test Todo Item',
            'description': 'This is a test todo item',
            'priority': 'medium',
            'due_date': (datetime.date.today() + datetime.timedelta(days=3)).isoformat()
        }
        
        create_response = self.api_client.post(API_ENDPOINTS['todo']['todos'], json=todo_data)
        assert_response_success(create_response, 201)
        created_todo = create_response.json()
        todo_id = created_todo['id']
        
        # Read
        get_response = self.api_client.get(f"{API_ENDPOINTS['todo']['todos']}/{todo_id}")
        assert_response_success(get_response, 200)
        retrieved_todo = get_response.json()
        assert retrieved_todo['title'] == todo_data['title']
        
        # Update
        update_data = {
            'title': 'Updated Todo Item',
            'status': 'in_progress',
            'priority': 'high'
        }
        
        update_response = self.api_client.put(f"{API_ENDPOINTS['todo']['todos']}/{todo_id}", json=update_data)
        assert_response_success(update_response, 200)
        updated_todo = update_response.json()
        assert updated_todo['title'] == update_data['title']
        assert updated_todo['status'] == update_data['status']
        
        # Delete
        delete_response = self.api_client.delete(f"{API_ENDPOINTS['todo']['todos']}/{todo_id}")
        assert delete_response.status_code in [200, 204]
        
        # Verify deletion
        get_deleted_response = self.api_client.get(f"{API_ENDPOINTS['todo']['todos']}/{todo_id}")
        assert_response_error(get_deleted_response, 404)
    
    def test_todo_status_transitions(self):
        """测试待办事项状态转换"""
        # 创建待办事项
        todo_data = {
            'title': 'Status Test Todo',
            'description': 'Testing status transitions'
        }
        
        create_response = self.api_client.post(API_ENDPOINTS['todo']['todos'], json=todo_data)
        assert_response_success(create_response, 201)
        todo_id = create_response.json()['id']
        
        # 测试状态转换: pending -> in_progress -> completed
        statuses = ['pending', 'in_progress', 'completed']
        
        for status in statuses:
            update_data = {'status': status}
            update_response = self.api_client.put(f"{API_ENDPOINTS['todo']['todos']}/{todo_id}", json=update_data)
            assert_response_success(update_response, 200)
            
            updated_todo = update_response.json()
            assert updated_todo['status'] == status
            
            # 如果状态是completed，应该有completed_at时间戳
            if status == 'completed':
                assert updated_todo['completed_at'] is not None
    
    def test_todo_current_focus_functionality(self):
        """测试当前焦点功能"""
        # 创建多个待办事项
        todos = []
        for i in range(3):
            todo_data = {
                'title': f'Focus Test Todo {i+1}',
                'description': f'Testing focus functionality {i+1}'
            }
            
            create_response = self.api_client.post(API_ENDPOINTS['todo']['todos'], json=todo_data)
            assert_response_success(create_response, 201)
            todos.append(create_response.json())
        
        # 设置第一个为当前焦点
        focus_data = {'is_current_focus': True}
        focus_response = self.api_client.put(f"{API_ENDPOINTS['todo']['todos']}/{todos[0]['id']}", json=focus_data)
        assert_response_success(focus_response, 200)
        
        # 验证焦点设置
        get_response = self.api_client.get(f"{API_ENDPOINTS['todo']['todos']}/{todos[0]['id']}")
        assert_response_success(get_response, 200)
        focused_todo = get_response.json()
        assert focused_todo['is_current_focus'] is True


class TestAchievementWorkflow:
    """测试成就管理工作流程"""

    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.api_client = APIClient()
        self.data_manager = TestDataManager(self.api_client)
        # 创建并登录测试用户
        self.user_data = self.data_manager.create_test_user()
        credentials = {
            'email': self.user_data['email'],
            'password': self.user_data['password']
        }
        login_response = self.api_client.login_user(credentials)
        assert_response_success(login_response, 200)
        yield
        self.data_manager.cleanup()

    def test_achievement_crud_operations(self):
        """测试成就的完整CRUD操作"""
        # Create
        achievement_data = {
            'title': 'Test Achievement',
            'description': 'This is a test achievement',
            'quantifiable_results': 'Increased efficiency by 50%',
            'core_skills_json': ['Python', 'Flask', 'Testing'],
            'date_achieved': datetime.date.today().isoformat()
        }

        create_response = self.api_client.post(API_ENDPOINTS['achievements']['list'], json=achievement_data)
        assert_response_success(create_response, 201)
        created_achievement = create_response.json()
        achievement_id = created_achievement['id']

        # Read
        get_response = self.api_client.get(f"{API_ENDPOINTS['achievements']['list']}/{achievement_id}")
        assert_response_success(get_response, 200)
        retrieved_achievement = get_response.json()
        assert retrieved_achievement['title'] == achievement_data['title']
        assert retrieved_achievement['core_skills_json'] == achievement_data['core_skills_json']

        # Update
        update_data = {
            'title': 'Updated Achievement',
            'quantifiable_results': 'Increased efficiency by 75%',
            'core_skills_json': ['Python', 'Django', 'Testing', 'Optimization']
        }

        update_response = self.api_client.put(f"{API_ENDPOINTS['achievements']['list']}/{achievement_id}", json=update_data)
        assert_response_success(update_response, 200)
        updated_achievement = update_response.json()
        assert updated_achievement['title'] == update_data['title']
        assert updated_achievement['quantifiable_results'] == update_data['quantifiable_results']

        # Delete
        delete_response = self.api_client.delete(f"{API_ENDPOINTS['achievements']['list']}/{achievement_id}")
        assert delete_response.status_code in [200, 204]

        # Verify deletion
        get_deleted_response = self.api_client.get(f"{API_ENDPOINTS['achievements']['list']}/{achievement_id}")
        assert_response_error(get_deleted_response, 404)


class TestPlanWorkflow:
    """测试计划管理工作流程"""

    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.api_client = APIClient()
        self.data_manager = TestDataManager(self.api_client)
        # 创建并登录测试用户
        self.user_data = self.data_manager.create_test_user()
        credentials = {
            'email': self.user_data['email'],
            'password': self.user_data['password']
        }
        login_response = self.api_client.login_user(credentials)
        assert_response_success(login_response, 200)
        yield
        self.data_manager.cleanup()

    def test_plan_crud_operations(self):
        """测试计划的完整CRUD操作"""
        # Create
        plan_data = {
            'goal_type': 'career',
            'title': 'Test Plan',
            'description': 'This is a test plan',
            'target_date': (datetime.date.today() + datetime.timedelta(days=30)).isoformat(),
            'status': 'active'
        }

        create_response = self.api_client.post(API_ENDPOINTS['plans']['list'], json=plan_data)
        assert_response_success(create_response, 201)
        created_plan = create_response.json()
        plan_id = created_plan['id']

        # Read
        get_response = self.api_client.get(f"{API_ENDPOINTS['plans']['list']}/{plan_id}")
        assert_response_success(get_response, 200)
        retrieved_plan = get_response.json()
        assert retrieved_plan['title'] == plan_data['title']
        assert retrieved_plan['goal_type'] == plan_data['goal_type']

        # Update
        update_data = {
            'title': 'Updated Plan',
            'status': 'completed',
            'description': 'This plan has been updated and completed'
        }

        update_response = self.api_client.put(f"{API_ENDPOINTS['plans']['list']}/{plan_id}", json=update_data)
        assert_response_success(update_response, 200)
        updated_plan = update_response.json()
        assert updated_plan['title'] == update_data['title']
        assert updated_plan['status'] == update_data['status']

        # Delete
        delete_response = self.api_client.delete(f"{API_ENDPOINTS['plans']['list']}/{plan_id}")
        assert delete_response.status_code in [200, 204]

        # Verify deletion
        get_deleted_response = self.api_client.get(f"{API_ENDPOINTS['plans']['list']}/{plan_id}")
        assert_response_error(get_deleted_response, 404)


class TestDataConsistency:
    """测试数据一致性和完整性"""

    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.api_client = APIClient()
        self.data_manager = TestDataManager(self.api_client)
        # 创建并登录测试用户
        self.user_data = self.data_manager.create_test_user()
        credentials = {
            'email': self.user_data['email'],
            'password': self.user_data['password']
        }
        login_response = self.api_client.login_user(credentials)
        assert_response_success(login_response, 200)
        yield
        self.data_manager.cleanup()

    def test_user_data_isolation(self):
        """测试用户数据隔离 - 确保用户只能访问自己的数据"""
        # 创建第二个用户
        user2_data = {
            'username': 'isolationuser2',
            'email': '<EMAIL>',
            'password': 'SecurePassword123!'
        }

        register_response = self.api_client.register_user(user2_data)
        assert_response_success(register_response, 201)

        # 用第一个用户创建数据
        todo_data = {
            'title': 'User 1 Todo',
            'description': 'This belongs to user 1'
        }

        todo_response = self.api_client.post(API_ENDPOINTS['todo']['todos'], json=todo_data)
        assert_response_success(todo_response, 201)
        user1_todo_id = todo_response.json()['id']

        # 切换到第二个用户
        self.api_client.clear_auth()
        credentials2 = {
            'email': user2_data['email'],
            'password': user2_data['password']
        }
        login_response2 = self.api_client.login_user(credentials2)
        assert_response_success(login_response2, 200)

        # 第二个用户尝试访问第一个用户的数据
        get_response = self.api_client.get(f"{API_ENDPOINTS['todo']['todos']}/{user1_todo_id}")
        assert_response_error(get_response, 404)  # 应该返回404，因为数据不属于当前用户

        # 第二个用户获取自己的待办事项列表（应该为空）
        todos_response = self.api_client.get(API_ENDPOINTS['todo']['todos'])
        assert_response_success(todos_response, 200)
        todos_list = todos_response.json()
        assert len(todos_list) == 0  # 第二个用户没有待办事项


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
