#!/usr/bin/env python3
"""
端到端测试
测试完整的用户流程，包括登录和访问受保护的页面
"""

import time
import urllib.request
import urllib.parse
import urllib.error
import json

# 测试配置
BACKEND_URL = 'http://127.0.0.1:5000'
FRONTEND_URL = 'http://localhost:5173'


class EndToEndTester:
    """端到端测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.access_token = None
    
    def log(self, message, level='INFO'):
        """日志输出"""
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
    
    def test_user_registration_and_login(self):
        """测试用户注册和登录流程"""
        self.log("Testing user registration and login...")
        
        # 生成唯一的测试用户
        timestamp = int(time.time())
        test_user = {
            'username': f'testuser_{timestamp}',
            'email': f'test_{timestamp}@example.com',
            'password': 'password123'
        }
        
        try:
            # 1. 注册用户
            register_data = json.dumps(test_user).encode('utf-8')
            register_req = urllib.request.Request(
                f"{BACKEND_URL}/api/v1/auth/register",
                data=register_data,
                headers={'Content-Type': 'application/json'}
            )
            
            with urllib.request.urlopen(register_req) as response:
                if response.getcode() == 200:
                    self.log(f"User registration successful: {test_user['email']}")
                else:
                    self.log(f"User registration failed: {response.getcode()}", 'ERROR')
                    return False
            
            # 2. 登录用户
            login_data = json.dumps({
                'email': test_user['email'],
                'password': test_user['password']
            }).encode('utf-8')
            
            login_req = urllib.request.Request(
                f"{BACKEND_URL}/api/v1/auth/login",
                data=login_data,
                headers={'Content-Type': 'application/json'}
            )
            
            with urllib.request.urlopen(login_req) as response:
                if response.getcode() == 200:
                    login_result = json.loads(response.read().decode('utf-8'))
                    self.access_token = login_result.get('access_token')
                    if self.access_token:
                        self.log("User login successful, token obtained")
                        self.test_results['auth_flow'] = True
                        return True
                    else:
                        self.log("Login successful but no token received", 'ERROR')
                        return False
                else:
                    self.log(f"User login failed: {response.getcode()}", 'ERROR')
                    return False
                    
        except Exception as e:
            self.log(f"Auth flow test failed: {e}", 'ERROR')
            self.test_results['auth_flow'] = False
            return False
    
    def test_protected_api_access(self):
        """测试受保护的API访问"""
        if not self.access_token:
            self.log("No access token available for API testing", 'ERROR')
            self.test_results['protected_api'] = False
            return False
        
        self.log("Testing protected API access...")
        
        try:
            # 测试获取todos
            req = urllib.request.Request(
                f"{BACKEND_URL}/api/v1/todo/todos",
                headers={'Authorization': f'Bearer {self.access_token}'}
            )
            
            with urllib.request.urlopen(req) as response:
                if response.getcode() == 200:
                    todos_data = json.loads(response.read().decode('utf-8'))
                    self.log(f"Protected API access successful, got {len(todos_data.get('data', []))} todos")
                    self.test_results['protected_api'] = True
                    return True
                else:
                    self.log(f"Protected API access failed: {response.getcode()}", 'ERROR')
                    self.test_results['protected_api'] = False
                    return False
                    
        except Exception as e:
            self.log(f"Protected API test failed: {e}", 'ERROR')
            self.test_results['protected_api'] = False
            return False
    
    def test_frontend_pages_accessibility(self):
        """测试前端页面可访问性"""
        self.log("Testing frontend pages accessibility...")
        
        pages_to_test = [
            ('/', 'Home'),
            ('/login', 'Login'),
            ('/register', 'Register'),
            ('/achievement', 'Achievement'),
            ('/plan', 'Plan'),
        ]
        
        all_accessible = True
        
        for path, name in pages_to_test:
            try:
                response = urllib.request.urlopen(f"{FRONTEND_URL}{path}", timeout=10)
                if response.getcode() == 200:
                    self.log(f"Page {path} ({name}) is accessible")
                else:
                    self.log(f"Page {path} returned {response.getcode()}", 'WARNING')
                    all_accessible = False
            except Exception as e:
                self.log(f"Failed to access page {path}: {e}", 'ERROR')
                all_accessible = False
        
        self.test_results['frontend_pages'] = all_accessible
        return all_accessible
    
    def test_protected_frontend_pages(self):
        """测试受保护的前端页面（需要认证）"""
        self.log("Testing protected frontend pages...")
        
        # 这些页面需要认证，应该重定向到登录页面或显示登录表单
        protected_pages = [
            ('/todo', 'Todo'),
            ('/anchor', 'Anchor'),
        ]
        
        for path, name in protected_pages:
            try:
                response = urllib.request.urlopen(f"{FRONTEND_URL}{path}", timeout=10)
                # 对于SPA，通常会返回200但内容可能是登录页面或重定向逻辑
                if response.getcode() == 200:
                    content = response.read().decode('utf-8')
                    # 检查是否包含登录相关的内容或重定向逻辑
                    if 'login' in content.lower() or 'authentication' in content.lower():
                        self.log(f"Protected page {path} correctly requires authentication")
                    else:
                        self.log(f"Protected page {path} is accessible (may be correct if user is logged in)")
                else:
                    self.log(f"Protected page {path} returned {response.getcode()}")
            except Exception as e:
                self.log(f"Error accessing protected page {path}: {e}", 'WARNING')
        
        # 对于受保护页面，我们主要检查它们不会直接返回404
        self.test_results['protected_pages'] = True
        return True
    
    def test_api_endpoints_exist(self):
        """测试API端点是否存在"""
        self.log("Testing API endpoints existence...")
        
        endpoints_to_test = [
            ('/api/v1/auth/register', 'POST', 'Auth Register'),
            ('/api/v1/auth/login', 'POST', 'Auth Login'),
            ('/api/v1/todo/todos', 'GET', 'Todo List'),
            ('/api/v1/achievements/', 'GET', 'Achievements'),
            ('/api/v1/plans/', 'GET', 'Plans'),
            ('/api/v1/anchor/profile', 'GET', 'Anchor Profile'),
        ]
        
        all_exist = True
        
        for endpoint, method, name in endpoints_to_test:
            try:
                if method == 'GET':
                    # GET请求可能需要认证，所以401是预期的
                    req = urllib.request.Request(f"{BACKEND_URL}{endpoint}")
                    try:
                        response = urllib.request.urlopen(req, timeout=5)
                        status = response.getcode()
                    except urllib.error.HTTPError as e:
                        status = e.code
                    
                    if status in [200, 401, 422]:  # 200=成功, 401=需要认证, 422=token无效
                        self.log(f"API endpoint {endpoint} ({name}) exists (status: {status})")
                    else:
                        self.log(f"API endpoint {endpoint} returned unexpected status: {status}", 'WARNING')
                        if status == 404:
                            all_exist = False
                
                elif method == 'POST':
                    # POST请求发送空数据，期望400或422（缺少必需字段）
                    req = urllib.request.Request(
                        f"{BACKEND_URL}{endpoint}",
                        data=b'{}',
                        headers={'Content-Type': 'application/json'}
                    )
                    try:
                        response = urllib.request.urlopen(req, timeout=5)
                        status = response.getcode()
                    except urllib.error.HTTPError as e:
                        status = e.code
                    
                    if status in [200, 400, 422]:  # 400/422=缺少必需字段
                        self.log(f"API endpoint {endpoint} ({name}) exists (status: {status})")
                    else:
                        self.log(f"API endpoint {endpoint} returned unexpected status: {status}", 'WARNING')
                        if status == 404:
                            all_exist = False
                            
            except Exception as e:
                self.log(f"Error testing endpoint {endpoint}: {e}", 'ERROR')
                all_exist = False
        
        self.test_results['api_endpoints'] = all_exist
        return all_exist
    
    def run_all_tests(self):
        """运行所有端到端测试"""
        self.log("Starting end-to-end tests...")
        
        # 1. 测试API端点存在性
        self.test_api_endpoints_exist()
        
        # 2. 测试前端页面可访问性
        self.test_frontend_pages_accessibility()
        
        # 3. 测试受保护的前端页面
        self.test_protected_frontend_pages()
        
        # 4. 测试用户认证流程
        if self.test_user_registration_and_login():
            # 5. 测试受保护的API访问
            self.test_protected_api_access()
        
        return all(self.test_results.values())
    
    def print_summary(self):
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print("📊 END-TO-END TEST SUMMARY")
        print("="*60)
        
        for test_name, passed in self.test_results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"{test_name.upper().replace('_', ' '):<25} {status}")
        
        all_passed = all(self.test_results.values())
        
        if all_passed:
            print("\n🎉 All end-to-end tests passed!")
            print("✅ Frontend and backend are working correctly")
            print("✅ Authentication flow is functional")
            print("✅ Protected routes are properly secured")
        else:
            print("\n💥 Some end-to-end tests failed!")
            failed_tests = [name for name, passed in self.test_results.items() if not passed]
            print(f"❌ Failed tests: {', '.join(failed_tests)}")
        
        print("="*60)
        
        return all_passed


def main():
    """主函数"""
    tester = EndToEndTester()
    
    try:
        success = tester.run_all_tests()
        tester.print_summary()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        tester.log("Test interrupted by user", 'WARNING')
        return 1
    except Exception as e:
        tester.log(f"Unexpected error: {e}", 'ERROR')
        return 1


if __name__ == '__main__':
    import sys
    sys.exit(main())
