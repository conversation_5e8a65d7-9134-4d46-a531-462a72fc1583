"""
测试配置文件
定义测试环境的配置参数
"""

import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

# 测试配置
class TestConfig:
    """测试环境配置"""
    
    # 基础配置
    TESTING = True
    DEBUG = True
    
    # 密钥配置
    SECRET_KEY = os.getenv('TEST_SECRET_KEY', 'test-secret-key-for-testing-only')
    JWT_SECRET_KEY = os.getenv('TEST_JWT_SECRET_KEY', 'test-jwt-secret-key-for-testing-only')
    
    # 数据库配置
    TEST_DATABASE_URL = os.getenv('TEST_DATABASE_URL', f'sqlite:///{PROJECT_ROOT}/test/test.db')
    SQLALCHEMY_DATABASE_URI = TEST_DATABASE_URL
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # API配置
    API_BASE_URL = os.getenv('TEST_API_BASE_URL', 'http://localhost:5000')
    API_VERSION = 'v1'
    API_PREFIX = f'/api/{API_VERSION}'
    
    # 前端配置
    FRONTEND_URL = os.getenv('TEST_FRONTEND_URL', 'http://localhost:5173')
    
    # 测试用户配置
    TEST_USER = {
        'username': 'testuser',
        'email': '<EMAIL>',
        'password': 'TestPassword123!'
    }
    
    TEST_ADMIN = {
        'username': 'testadmin',
        'email': '<EMAIL>',
        'password': 'AdminPassword123!'
    }
    
    # 超时配置
    REQUEST_TIMEOUT = 30
    STARTUP_TIMEOUT = 60
    
    # 测试数据配置
    CLEANUP_AFTER_TESTS = True
    USE_FIXTURES = True
    
    # 日志配置
    LOG_LEVEL = 'DEBUG'
    LOG_FILE = PROJECT_ROOT / 'test' / 'test.log'
    
    # 浏览器测试配置
    BROWSER_CONFIG = {
        'headless': True,
        'window_size': (1920, 1080),
        'timeout': 30000,
        'slow_mo': 0  # 毫秒，用于调试
    }
    
    # 并发测试配置
    MAX_WORKERS = 4
    
    @classmethod
    def get_api_url(cls, endpoint=''):
        """获取完整的API URL"""
        return f"{cls.API_BASE_URL}{cls.API_PREFIX}{endpoint}"
    
    @classmethod
    def get_frontend_url(cls, path=''):
        """获取完整的前端URL"""
        return f"{cls.FRONTEND_URL}{path}"

# 测试环境变量
TEST_ENV = {
    'FLASK_CONFIG': 'testing',
    'SECRET_KEY': TestConfig.SECRET_KEY,
    'JWT_SECRET_KEY': TestConfig.JWT_SECRET_KEY,
    'DATABASE_URL': TestConfig.TEST_DATABASE_URL,
    'TESTING': 'true'
}

# API端点配置
API_ENDPOINTS = {
    'auth': {
        'register': '/auth/register',
        'login': '/auth/login',
        'logout': '/auth/logout',
        'refresh': '/auth/refresh',
        'me': '/auth/me',
        'change_password': '/auth/change-password'
    },
    'todo': {
        'list': '/todo/todos',
        'create': '/todo/todos',
        'detail': '/todo/todos/{id}',
        'update': '/todo/todos/{id}',
        'delete': '/todo/todos/{id}'
    },
    'achievements': {
        'list': '/achievements/',
        'create': '/achievements/',
        'detail': '/achievements/{id}',
        'update': '/achievements/{id}',
        'delete': '/achievements/{id}'
    },
    'plans': {
        'list': '/plans/',
        'create': '/plans/',
        'detail': '/plans/{id}',
        'update': '/plans/{id}',
        'delete': '/plans/{id}'
    },
    'anchor': {
        'profile': '/anchor/profile'
    }
}

# 前端路由配置
FRONTEND_ROUTES = {
    'login': '/login',
    'register': '/register',
    'todo': '/todo',
    'achievement': '/achievement',
    'plan': '/plan',
    'anchor': '/anchor'
}

# 测试数据模板
TEST_DATA_TEMPLATES = {
    'todo': {
        'title': 'Test Todo Item',
        'description': 'This is a test todo item',
        'status': 'pending',
        'priority': 'medium'
    },
    'achievement': {
        'title': 'Test Achievement',
        'description': 'This is a test achievement',
        'quantifiable_results': 'Completed 100% of test cases',
        'core_skills_json': ['Testing', 'Python', 'API Development']
    },
    'plan': {
        'title': 'Test Future Plan',
        'description': 'This is a test future plan',
        'goal_type': 'personal',
        'status': 'active'
    }
}
