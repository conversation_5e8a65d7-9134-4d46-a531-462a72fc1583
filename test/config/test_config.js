/**
 * JavaScript测试配置文件
 * 用于前端和集成测试
 */

const path = require('path');

// 项目根目录
const PROJECT_ROOT = path.resolve(__dirname, '../../');

// 测试配置
const testConfig = {
  // 基础配置
  testing: true,
  debug: true,
  
  // API配置
  api: {
    baseUrl: process.env.TEST_API_BASE_URL || 'http://localhost:5000',
    version: 'v1',
    timeout: 30000,
    retries: 3
  },
  
  // 前端配置
  frontend: {
    url: process.env.TEST_FRONTEND_URL || 'http://localhost:5173',
    timeout: 30000
  },
  
  // 测试用户配置
  testUser: {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },
  
  testAdmin: {
    username: 'testadmin',
    email: '<EMAIL>',
    password: 'AdminPassword123!'
  },
  
  // 浏览器配置
  browser: {
    headless: process.env.CI === 'true' || process.env.HEADLESS === 'true',
    viewport: {
      width: 1920,
      height: 1080
    },
    timeout: 30000,
    slowMo: parseInt(process.env.SLOW_MO) || 0
  },
  
  // 测试超时配置
  timeouts: {
    test: 60000,
    beforeAll: 120000,
    afterAll: 30000
  },
  
  // 路径配置
  paths: {
    projectRoot: PROJECT_ROOT,
    screenshots: path.join(PROJECT_ROOT, 'test/screenshots'),
    reports: path.join(PROJECT_ROOT, 'test/reports'),
    fixtures: path.join(PROJECT_ROOT, 'test/fixtures')
  }
};

// API端点配置
const apiEndpoints = {
  auth: {
    register: '/api/v1/auth/register',
    login: '/api/v1/auth/login',
    logout: '/api/v1/auth/logout',
    refresh: '/api/v1/auth/refresh',
    me: '/api/v1/auth/me',
    changePassword: '/api/v1/auth/change-password'
  },
  todo: {
    list: '/api/v1/todo/todos',
    create: '/api/v1/todo/todos',
    detail: (id) => `/api/v1/todo/todos/${id}`,
    update: (id) => `/api/v1/todo/todos/${id}`,
    delete: (id) => `/api/v1/todo/todos/${id}`
  },
  achievements: {
    list: '/api/v1/achievements/',
    create: '/api/v1/achievements/',
    detail: (id) => `/api/v1/achievements/${id}`,
    update: (id) => `/api/v1/achievements/${id}`,
    delete: (id) => `/api/v1/achievements/${id}`
  },
  plans: {
    list: '/api/v1/plans/',
    create: '/api/v1/plans/',
    detail: (id) => `/api/v1/plans/${id}`,
    update: (id) => `/api/v1/plans/${id}`,
    delete: (id) => `/api/v1/plans/${id}`
  },
  anchor: {
    profile: '/api/v1/anchor/profile'
  }
};

// 前端路由配置
const frontendRoutes = {
  login: '/login',
  register: '/register',
  todo: '/todo',
  achievement: '/achievement',
  plan: '/plan',
  anchor: '/anchor'
};

// 测试数据模板
const testDataTemplates = {
  todo: {
    title: 'Test Todo Item',
    description: 'This is a test todo item',
    status: 'pending',
    priority: 'medium'
  },
  achievement: {
    title: 'Test Achievement',
    description: 'This is a test achievement',
    quantifiable_results: 'Completed 100% of test cases',
    core_skills_json: ['Testing', 'JavaScript', 'API Development']
  },
  plan: {
    title: 'Test Future Plan',
    description: 'This is a test future plan',
    goal_type: 'personal',
    status: 'active'
  }
};

// 辅助函数
const helpers = {
  /**
   * 获取完整的API URL
   * @param {string} endpoint - API端点
   * @returns {string} 完整的API URL
   */
  getApiUrl(endpoint = '') {
    return `${testConfig.api.baseUrl}${endpoint}`;
  },
  
  /**
   * 获取完整的前端URL
   * @param {string} path - 前端路径
   * @returns {string} 完整的前端URL
   */
  getFrontendUrl(path = '') {
    return `${testConfig.frontend.url}${path}`;
  },
  
  /**
   * 等待指定时间
   * @param {number} ms - 等待时间（毫秒）
   * @returns {Promise} Promise对象
   */
  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  
  /**
   * 生成随机字符串
   * @param {number} length - 字符串长度
   * @returns {string} 随机字符串
   */
  randomString(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  /**
   * 生成测试用户数据
   * @returns {object} 测试用户数据
   */
  generateTestUser() {
    const suffix = this.randomString(4);
    return {
      username: `testuser_${suffix}`,
      email: `test_${suffix}@example.com`,
      password: 'TestPassword123!'
    };
  }
};

module.exports = {
  testConfig,
  apiEndpoints,
  frontendRoutes,
  testDataTemplates,
  helpers
};
