"""
应用启动集成测试
测试应用是否能够正常启动和运行
"""

import pytest
import time
import requests
import subprocess
import sys
from pathlib import Path
from test.utils.test_helpers import ProcessManager, APIClient
from test.config.test_config import TestConfig, FRONTEND_ROUTES


class TestAppStartup:
    """应用启动测试类"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.process_manager = ProcessManager()
        self.api_client = APIClient()
        yield
        # 清理
        self.process_manager.stop_all()
    
    def test_backend_startup(self):
        """测试后端服务启动"""
        # 启动后端服务
        backend_process = self.process_manager.start_backend()
        
        # 等待服务启动
        startup_success = self.process_manager.wait_for_service(
            TestConfig.API_BASE_URL, 
            timeout=TestConfig.STARTUP_TIMEOUT
        )
        
        assert startup_success, "Backend service failed to start within timeout"
        
        # 验证进程仍在运行
        assert backend_process.poll() is None, "Backend process terminated unexpectedly"
        
        # 测试基本API响应
        try:
            response = requests.get(TestConfig.API_BASE_URL, timeout=10)
            assert response.status_code < 500, f"Backend returned server error: {response.status_code}"
        except requests.RequestException as e:
            pytest.fail(f"Failed to connect to backend: {e}")
    
    def test_backend_api_endpoints(self):
        """测试后端API端点可访问性"""
        # 启动后端服务
        backend_process = self.process_manager.start_backend()
        
        # 等待服务启动
        startup_success = self.process_manager.wait_for_service(
            TestConfig.API_BASE_URL, 
            timeout=TestConfig.STARTUP_TIMEOUT
        )
        
        assert startup_success, "Backend service failed to start"
        
        # 测试各个API端点
        test_endpoints = [
            # 认证端点（不需要认证的）
            ('/api/v1/auth/register', 'POST', {'username': 'test', 'email': '<EMAIL>', 'password': 'test123'}),
            # 其他端点会返回401，这是正常的
            ('/api/v1/todo/todos', 'GET', None),
            ('/api/v1/achievements/', 'GET', None),
            ('/api/v1/plans/', 'GET', None),
            ('/api/v1/anchor/profile', 'GET', None),
        ]
        
        for endpoint, method, data in test_endpoints:
            url = f"{TestConfig.API_BASE_URL}{endpoint}"
            try:
                if method == 'GET':
                    response = requests.get(url, timeout=10)
                elif method == 'POST':
                    response = requests.post(url, json=data, timeout=10)
                
                # 检查响应不是服务器错误（5xx）
                assert response.status_code < 500, f"Server error on {endpoint}: {response.status_code}"
                
                # 对于需要认证的端点，401是正常的
                if endpoint != '/api/v1/auth/register':
                    assert response.status_code in [200, 201, 401, 400], f"Unexpected status on {endpoint}: {response.status_code}"
                
            except requests.RequestException as e:
                pytest.fail(f"Failed to connect to {endpoint}: {e}")
    
    def test_frontend_startup(self):
        """测试前端服务启动"""
        # 检查是否有npm
        try:
            subprocess.run(['npm', '--version'], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            pytest.skip("npm not available, skipping frontend startup test")
        
        # 检查前端目录是否存在
        frontend_dir = Path(__file__).parent.parent.parent / 'svelte@latest'
        if not frontend_dir.exists():
            pytest.skip("Frontend directory not found, skipping frontend startup test")
        
        # 检查package.json是否存在
        package_json = frontend_dir / 'package.json'
        if not package_json.exists():
            pytest.skip("package.json not found, skipping frontend startup test")
        
        # 启动前端服务
        frontend_process = self.process_manager.start_frontend()
        
        # 等待服务启动
        startup_success = self.process_manager.wait_for_service(
            TestConfig.FRONTEND_URL, 
            timeout=TestConfig.STARTUP_TIMEOUT
        )
        
        assert startup_success, "Frontend service failed to start within timeout"
        
        # 验证进程仍在运行
        assert frontend_process.poll() is None, "Frontend process terminated unexpectedly"
        
        # 测试基本页面响应
        try:
            response = requests.get(TestConfig.FRONTEND_URL, timeout=10)
            assert response.status_code == 200, f"Frontend returned error: {response.status_code}"
            assert 'html' in response.headers.get('content-type', '').lower(), "Frontend did not return HTML"
        except requests.RequestException as e:
            pytest.fail(f"Failed to connect to frontend: {e}")
    
    def test_frontend_routes(self):
        """测试前端路由可访问性"""
        # 检查npm和前端目录
        try:
            subprocess.run(['npm', '--version'], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            pytest.skip("npm not available, skipping frontend routes test")
        
        frontend_dir = Path(__file__).parent.parent.parent / 'svelte@latest'
        if not frontend_dir.exists() or not (frontend_dir / 'package.json').exists():
            pytest.skip("Frontend not available, skipping frontend routes test")
        
        # 启动前端服务
        frontend_process = self.process_manager.start_frontend()
        
        # 等待服务启动
        startup_success = self.process_manager.wait_for_service(
            TestConfig.FRONTEND_URL, 
            timeout=TestConfig.STARTUP_TIMEOUT
        )
        
        assert startup_success, "Frontend service failed to start"
        
        # 测试各个前端路由
        for route_name, route_path in FRONTEND_ROUTES.items():
            url = f"{TestConfig.FRONTEND_URL}{route_path}"
            try:
                response = requests.get(url, timeout=10)
                # SPA应用所有路由都应该返回200（由于fallback到index.html）
                assert response.status_code == 200, f"Route {route_path} returned {response.status_code}"
                assert 'html' in response.headers.get('content-type', '').lower(), f"Route {route_path} did not return HTML"
            except requests.RequestException as e:
                pytest.fail(f"Failed to access route {route_path}: {e}")
    
    def test_full_stack_startup(self):
        """测试全栈应用启动"""
        # 启动后端服务
        backend_process = self.process_manager.start_backend()
        
        # 等待后端启动
        backend_startup = self.process_manager.wait_for_service(
            TestConfig.API_BASE_URL, 
            timeout=TestConfig.STARTUP_TIMEOUT
        )
        
        assert backend_startup, "Backend service failed to start"
        
        # 检查前端是否可用
        try:
            subprocess.run(['npm', '--version'], check=True, capture_output=True)
            frontend_dir = Path(__file__).parent.parent.parent / 'svelte@latest'
            
            if frontend_dir.exists() and (frontend_dir / 'package.json').exists():
                # 启动前端服务
                frontend_process = self.process_manager.start_frontend()
                
                # 等待前端启动
                frontend_startup = self.process_manager.wait_for_service(
                    TestConfig.FRONTEND_URL, 
                    timeout=TestConfig.STARTUP_TIMEOUT
                )
                
                assert frontend_startup, "Frontend service failed to start"
                
                # 验证两个服务都在运行
                assert backend_process.poll() is None, "Backend process terminated"
                assert frontend_process.poll() is None, "Frontend process terminated"
                
                # 测试前后端通信
                # 这里可以添加更复杂的集成测试
                backend_response = requests.get(TestConfig.API_BASE_URL, timeout=10)
                frontend_response = requests.get(TestConfig.FRONTEND_URL, timeout=10)
                
                assert backend_response.status_code < 500, "Backend not responding properly"
                assert frontend_response.status_code == 200, "Frontend not responding properly"
            else:
                pytest.skip("Frontend not available, testing backend only")
                
        except (subprocess.CalledProcessError, FileNotFoundError):
            pytest.skip("npm not available, testing backend only")
    
    def test_database_connection(self):
        """测试数据库连接"""
        # 启动后端服务
        backend_process = self.process_manager.start_backend()
        
        # 等待服务启动
        startup_success = self.process_manager.wait_for_service(
            TestConfig.API_BASE_URL, 
            timeout=TestConfig.STARTUP_TIMEOUT
        )
        
        assert startup_success, "Backend service failed to start"
        
        # 尝试注册用户来测试数据库连接
        test_user = {
            'username': 'dbtest_user',
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        }
        
        try:
            response = self.api_client.register_user(test_user)
            # 如果数据库连接正常，应该能成功注册或返回合理的错误
            assert response.status_code in [200, 201, 400], f"Database connection issue: {response.status_code}"
            
            # 如果注册成功，尝试登录
            if response.status_code in [200, 201]:
                login_response = self.api_client.login_user({
                    'email': test_user['email'],
                    'password': test_user['password']
                })
                assert login_response.status_code in [200, 201], "Login failed after successful registration"
                
        except requests.RequestException as e:
            pytest.fail(f"Database connection test failed: {e}")
    
    def test_environment_variables(self):
        """测试环境变量配置"""
        # 检查关键环境变量
        import os
        
        # 这些环境变量应该在测试环境中设置
        required_env_vars = [
            'SECRET_KEY',
            'JWT_SECRET_KEY'
        ]
        
        for var in required_env_vars:
            value = os.getenv(var)
            assert value is not None, f"Environment variable {var} is not set"
            assert len(value) > 0, f"Environment variable {var} is empty"
    
    def test_config_validation(self):
        """测试配置验证"""
        # 验证测试配置的完整性
        assert TestConfig.API_BASE_URL, "API_BASE_URL not configured"
        assert TestConfig.FRONTEND_URL, "FRONTEND_URL not configured"
        assert TestConfig.SECRET_KEY, "SECRET_KEY not configured"
        assert TestConfig.JWT_SECRET_KEY, "JWT_SECRET_KEY not configured"
        
        # 验证超时配置合理
        assert TestConfig.REQUEST_TIMEOUT > 0, "REQUEST_TIMEOUT must be positive"
        assert TestConfig.STARTUP_TIMEOUT > 0, "STARTUP_TIMEOUT must be positive"
        
        # 验证测试用户配置
        assert TestConfig.TEST_USER['username'], "TEST_USER username not configured"
        assert TestConfig.TEST_USER['email'], "TEST_USER email not configured"
        assert TestConfig.TEST_USER['password'], "TEST_USER password not configured"


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
