#!/usr/bin/env python3
"""
测试现有运行的后端服务
假设后端已经在运行，直接测试API功能
"""

import json
import time
import urllib.request
import urllib.parse
import urllib.error

# 测试配置
API_BASE_URL = 'http://127.0.0.1:5000'


class BackendTester:
    """后端测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.access_token = None
    
    def log(self, message, level='INFO'):
        """日志输出"""
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
    
    def make_request(self, endpoint, method='GET', data=None, headers=None):
        """发送HTTP请求"""
        url = f"{API_BASE_URL}{endpoint}"
        
        if headers is None:
            headers = {}
        
        if data is not None:
            data = json.dumps(data).encode('utf-8')
            headers['Content-Type'] = 'application/json'
        
        if self.access_token:
            headers['Authorization'] = f'Bearer {self.access_token}'
        
        try:
            req = urllib.request.Request(url, data=data, headers=headers)
            req.get_method = lambda: method
            
            response = urllib.request.urlopen(req, timeout=10)
            response_data = response.read().decode('utf-8')

            # 尝试解析JSON，如果失败则返回原始文本
            try:
                parsed_data = json.loads(response_data) if response_data else None
            except json.JSONDecodeError:
                parsed_data = response_data

            return {
                'status_code': response.getcode(),
                'data': parsed_data
            }
            
        except urllib.error.HTTPError as e:
            error_data = e.read().decode('utf-8') if e.fp else ''
            try:
                parsed_error = json.loads(error_data) if error_data else None
            except json.JSONDecodeError:
                parsed_error = error_data
            return {
                'status_code': e.code,
                'data': parsed_error,
                'error': str(e)
            }
        except Exception as e:
            return {
                'status_code': 0,
                'error': str(e)
            }
    
    def test_backend_health(self):
        """测试后端健康状态"""
        self.log("Testing backend health...")
        
        response = self.make_request('/')
        
        if response['status_code'] == 0:
            self.log(f"Backend not accessible: {response.get('error')}", 'ERROR')
            self.test_results['backend_health'] = False
            return False
        
        if response['status_code'] >= 500:
            self.log(f"Backend server error: {response['status_code']}", 'ERROR')
            self.test_results['backend_health'] = False
            return False
        
        self.log("Backend is healthy", 'SUCCESS')
        self.test_results['backend_health'] = True
        return True
    
    def test_api_endpoints_accessibility(self):
        """测试API端点可访问性"""
        self.log("Testing API endpoints accessibility...")
        
        endpoints = [
            ('/api/v1/auth/register', 'POST'),
            ('/api/v1/todo/todos', 'GET'),
            ('/api/v1/achievements/', 'GET'),
            ('/api/v1/plans/', 'GET'),
            ('/api/v1/anchor/profile', 'GET'),
        ]
        
        all_accessible = True
        
        for endpoint, method in endpoints:
            response = self.make_request(endpoint, method, {} if method == 'POST' else None)
            
            if response['status_code'] == 0:
                self.log(f"Cannot connect to {endpoint}: {response.get('error')}", 'ERROR')
                all_accessible = False
            elif response['status_code'] >= 500:
                self.log(f"Server error on {endpoint}: {response['status_code']}", 'ERROR')
                all_accessible = False
            else:
                self.log(f"Endpoint {endpoint} accessible (status: {response['status_code']})")
        
        self.test_results['api_accessibility'] = all_accessible
        return all_accessible
    
    def test_user_registration(self):
        """测试用户注册"""
        self.log("Testing user registration...")
        
        test_user = {
            'username': f'testuser_{int(time.time())}',
            'email': f'test_{int(time.time())}@example.com',
            'password': 'TestPassword123!'
        }
        
        response = self.make_request('/api/v1/auth/register', 'POST', test_user)
        
        if response['status_code'] not in [200, 201]:
            self.log(f"Registration failed: {response['status_code']} - {response.get('data', response.get('error'))}", 'ERROR')
            self.test_results['user_registration'] = False
            return False, None
        
        self.log("User registration successful", 'SUCCESS')
        self.test_results['user_registration'] = True
        return True, test_user
    
    def test_user_login(self, user_data):
        """测试用户登录"""
        self.log("Testing user login...")
        
        login_data = {
            'email': user_data['email'],
            'password': user_data['password']
        }
        
        response = self.make_request('/api/v1/auth/login', 'POST', login_data)
        
        if response['status_code'] not in [200, 201]:
            self.log(f"Login failed: {response['status_code']} - {response.get('data', response.get('error'))}", 'ERROR')
            self.test_results['user_login'] = False
            return False
        
        if 'access_token' not in response['data']:
            self.log("Login response missing access_token", 'ERROR')
            self.test_results['user_login'] = False
            return False
        
        self.access_token = response['data']['access_token']
        self.log("User login successful", 'SUCCESS')
        self.test_results['user_login'] = True
        return True
    
    def test_authenticated_endpoints(self):
        """测试需要认证的端点"""
        self.log("Testing authenticated endpoints...")
        
        if not self.access_token:
            self.log("No access token available for authenticated tests", 'ERROR')
            self.test_results['authenticated_endpoints'] = False
            return False
        
        # 测试获取当前用户信息
        response = self.make_request('/api/v1/auth/me', 'GET')
        
        if response['status_code'] not in [200, 201]:
            self.log(f"Get current user failed: {response['status_code']}", 'ERROR')
            self.test_results['authenticated_endpoints'] = False
            return False
        
        self.log("Authenticated endpoints working", 'SUCCESS')
        self.test_results['authenticated_endpoints'] = True
        return True
    
    def test_todo_crud_operations(self):
        """测试Todo CRUD操作"""
        self.log("Testing Todo CRUD operations...")
        
        if not self.access_token:
            self.log("No access token for Todo tests", 'ERROR')
            self.test_results['todo_crud'] = False
            return False
        
        # 创建Todo
        todo_data = {
            'title': 'Test Todo Item',
            'description': 'This is a test todo item',
            'status': 'pending',
            'priority': 'medium'
        }
        
        create_response = self.make_request('/api/v1/todo/todos', 'POST', todo_data)
        
        if create_response['status_code'] not in [200, 201]:
            self.log(f"Todo creation failed: {create_response['status_code']}", 'ERROR')
            self.test_results['todo_crud'] = False
            return False
        
        # 解析嵌套的响应数据结构
        response_data = create_response['data']
        if isinstance(response_data, dict) and 'data' in response_data:
            todo_data = response_data['data']
        else:
            todo_data = response_data

        todo_id = todo_data.get('id') if isinstance(todo_data, dict) else None
        if not todo_id:
            self.log(f"Todo creation response missing ID. Response structure: {create_response['data']}", 'ERROR')
            self.test_results['todo_crud'] = False
            return False
        
        # 获取Todo列表
        list_response = self.make_request('/api/v1/todo/todos', 'GET')
        
        if list_response['status_code'] not in [200, 201]:
            self.log(f"Todo list retrieval failed: {list_response['status_code']}", 'ERROR')
            self.test_results['todo_crud'] = False
            return False
        
        # 删除Todo
        delete_response = self.make_request(f'/api/v1/todo/todos/{todo_id}', 'DELETE')
        
        if delete_response['status_code'] not in [200, 204]:
            self.log(f"Todo deletion failed: {delete_response['status_code']}", 'ERROR')
            self.test_results['todo_crud'] = False
            return False
        
        self.log("Todo CRUD operations successful", 'SUCCESS')
        self.test_results['todo_crud'] = True
        return True
    
    def test_route_naming_compliance(self):
        """测试路由命名规范合规性"""
        self.log("Testing route naming compliance...")
        
        # 测试正确的路由是否存在
        correct_routes = [
            '/api/v1/achievements/',  # 应该是achievements，不是done
            '/api/v1/todo/todos',     # 应该是todo，不是doing
            '/api/v1/plans/',         # 应该是plans，保持不变
        ]
        
        all_compliant = True
        
        for route in correct_routes:
            response = self.make_request(route, 'GET')
            
            if response['status_code'] == 404:
                self.log(f"Route {route} not found - naming compliance issue", 'ERROR')
                all_compliant = False
            elif response['status_code'] >= 500:
                self.log(f"Server error on {route}: {response['status_code']}", 'ERROR')
                all_compliant = False
            else:
                self.log(f"Route {route} exists and accessible")
        
        self.test_results['route_naming'] = all_compliant
        return all_compliant
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log("Starting backend API tests...")
        
        # 基础健康检查
        if not self.test_backend_health():
            self.log("Backend health check failed, skipping other tests", 'ERROR')
            return False
        
        # API端点可访问性
        self.test_api_endpoints_accessibility()
        
        # 路由命名合规性
        self.test_route_naming_compliance()
        
        # 用户认证流程
        registration_success, user_data = self.test_user_registration()
        
        if registration_success and user_data:
            login_success = self.test_user_login(user_data)
            
            if login_success:
                self.test_authenticated_endpoints()
                self.test_todo_crud_operations()
        
        return all(self.test_results.values())
    
    def print_summary(self):
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print("📊 BACKEND API TEST SUMMARY")
        print("="*60)
        
        for test_name, passed in self.test_results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"{test_name.upper().replace('_', ' '):<25} {status}")
        
        all_passed = all(self.test_results.values())
        
        if all_passed:
            print("\n🎉 All backend tests passed!")
        else:
            print("\n💥 Some backend tests failed!")
        
        print("="*60)
        
        return all_passed


def main():
    """主函数"""
    tester = BackendTester()
    
    try:
        success = tester.run_all_tests()
        tester.print_summary()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        tester.log("Test interrupted by user", 'WARNING')
        return 1
    except Exception as e:
        tester.log(f"Unexpected error: {e}", 'ERROR')
        return 1


if __name__ == '__main__':
    import sys
    sys.exit(main())
