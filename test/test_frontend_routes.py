#!/usr/bin/env python3
"""
前端路由测试
验证前端页面路由是否按照新的命名规范正常工作
"""

import time
import urllib.request
import urllib.error

# 测试配置
FRONTEND_URL = 'http://localhost:5173'


class FrontendRouteTester:
    """前端路由测试器"""
    
    def __init__(self):
        self.test_results = {}
    
    def log(self, message, level='INFO'):
        """日志输出"""
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
    
    def test_route(self, route_path, route_name):
        """测试单个路由"""
        url = f"{FRONTEND_URL}{route_path}"
        
        try:
            response = urllib.request.urlopen(url, timeout=10)
            status_code = response.getcode()
            content_type = response.headers.get('content-type', '').lower()
            
            if status_code == 200 and 'html' in content_type:
                self.log(f"Route {route_path} ({route_name}) is accessible", 'SUCCESS')
                return True
            else:
                self.log(f"Route {route_path} returned unexpected response: {status_code}", 'ERROR')
                return False
                
        except Exception as e:
            self.log(f"Failed to access route {route_path}: {e}", 'ERROR')
            return False
    
    def test_frontend_health(self):
        """测试前端健康状态"""
        self.log("Testing frontend health...")
        
        try:
            response = urllib.request.urlopen(FRONTEND_URL, timeout=10)
            if response.getcode() == 200:
                self.log("Frontend is healthy", 'SUCCESS')
                self.test_results['frontend_health'] = True
                return True
            else:
                self.log(f"Frontend returned status {response.getcode()}", 'ERROR')
                self.test_results['frontend_health'] = False
                return False
        except Exception as e:
            self.log(f"Frontend not accessible: {e}", 'ERROR')
            self.test_results['frontend_health'] = False
            return False
    
    def test_route_naming_compliance(self):
        """测试路由命名规范合规性"""
        self.log("Testing route naming compliance...")
        
        # 根据用户要求的命名规范测试路由
        routes_to_test = [
            ('/achievement', 'Achievement Page'),  # 应该是 /achievement，不是 /done
            ('/todo', 'Todo Page'),               # 应该是 /todo，不是 /doing
            ('/plan', 'Plan Page'),               # 保持 /plan 不变
            ('/login', 'Login Page'),
            ('/register', 'Register Page'),
            ('/anchor', 'Anchor Page'),
        ]
        
        all_compliant = True
        
        for route_path, route_name in routes_to_test:
            if not self.test_route(route_path, route_name):
                all_compliant = False
        
        self.test_results['route_naming'] = all_compliant
        return all_compliant
    
    def test_legacy_routes_redirect(self):
        """测试旧路由是否正确重定向或不存在"""
        self.log("Testing legacy routes...")
        
        # 这些旧路由应该不存在或重定向到新路由
        legacy_routes = [
            '/done',    # 应该重定向到 /achievement 或返回404
            '/doing',   # 应该重定向到 /todo 或返回404
        ]
        
        for route_path in legacy_routes:
            url = f"{FRONTEND_URL}{route_path}"
            try:
                response = urllib.request.urlopen(url, timeout=10)
                # 如果返回200，检查是否重定向到了正确的页面
                if response.getcode() == 200:
                    final_url = response.geturl()
                    if final_url != url:
                        self.log(f"Legacy route {route_path} redirects to {final_url}", 'INFO')
                    else:
                        self.log(f"Legacy route {route_path} still exists (should be removed)", 'WARNING')
                else:
                    self.log(f"Legacy route {route_path} returns {response.getcode()}", 'INFO')
            except urllib.error.HTTPError as e:
                if e.code == 404:
                    self.log(f"Legacy route {route_path} correctly returns 404", 'SUCCESS')
                else:
                    self.log(f"Legacy route {route_path} returns {e.code}", 'INFO')
            except Exception as e:
                self.log(f"Error testing legacy route {route_path}: {e}", 'WARNING')
        
        # 对于SPA，所有路由通常都返回200，所以这个测试主要是信息性的
        self.test_results['legacy_routes'] = True
        return True
    
    def test_navigation_flow(self):
        """测试导航流程"""
        self.log("Testing navigation flow...")
        
        # 测试主要的导航路径
        navigation_flow = [
            ('/', 'Home/Root'),
            ('/login', 'Login'),
            ('/register', 'Register'),
            ('/todo', 'Todo'),
            ('/achievement', 'Achievement'),
            ('/plan', 'Plan'),
            ('/anchor', 'Anchor'),
        ]
        
        all_accessible = True
        
        for route_path, route_name in navigation_flow:
            if not self.test_route(route_path, route_name):
                all_accessible = False
        
        self.test_results['navigation_flow'] = all_accessible
        return all_accessible
    
    def run_all_tests(self):
        """运行所有前端测试"""
        self.log("Starting frontend route tests...")
        
        # 基础健康检查
        if not self.test_frontend_health():
            self.log("Frontend health check failed, skipping other tests", 'ERROR')
            return False
        
        # 路由命名合规性
        self.test_route_naming_compliance()
        
        # 旧路由检查
        self.test_legacy_routes_redirect()
        
        # 导航流程
        self.test_navigation_flow()
        
        return all(self.test_results.values())
    
    def print_summary(self):
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print("📊 FRONTEND ROUTE TEST SUMMARY")
        print("="*60)
        
        for test_name, passed in self.test_results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"{test_name.upper().replace('_', ' '):<25} {status}")
        
        all_passed = all(self.test_results.values())
        
        if all_passed:
            print("\n🎉 All frontend tests passed!")
        else:
            print("\n💥 Some frontend tests failed!")
        
        print("="*60)
        
        return all_passed


def main():
    """主函数"""
    tester = FrontendRouteTester()
    
    try:
        success = tester.run_all_tests()
        tester.print_summary()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        tester.log("Test interrupted by user", 'WARNING')
        return 1
    except Exception as e:
        tester.log(f"Unexpected error: {e}", 'ERROR')
        return 1


if __name__ == '__main__':
    import sys
    sys.exit(main())
