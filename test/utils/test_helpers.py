"""
测试辅助工具
提供测试中常用的辅助函数和工具类
"""

import os
import sys
import time
import json
import requests
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional, List
from contextlib import contextmanager

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from test.config.test_config import TestConfig, API_ENDPOINTS, TEST_DATA_TEMPLATES


class APIClient:
    """API客户端，用于测试API端点"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or TestConfig.API_BASE_URL
        self.session = requests.Session()
        self.session.timeout = TestConfig.REQUEST_TIMEOUT
        self.access_token = None
        self.refresh_token = None
    
    def set_auth_token(self, token: str):
        """设置认证令牌"""
        self.access_token = token
        self.session.headers.update({'Authorization': f'Bearer {token}'})
    
    def clear_auth(self):
        """清除认证信息"""
        self.access_token = None
        self.refresh_token = None
        if 'Authorization' in self.session.headers:
            del self.session.headers['Authorization']
    
    def request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发送HTTP请求"""
        url = f"{self.base_url}{TestConfig.API_PREFIX}{endpoint}"
        response = self.session.request(method, url, **kwargs)
        return response
    
    def get(self, endpoint: str, **kwargs) -> requests.Response:
        """发送GET请求"""
        return self.request('GET', endpoint, **kwargs)
    
    def post(self, endpoint: str, **kwargs) -> requests.Response:
        """发送POST请求"""
        return self.request('POST', endpoint, **kwargs)
    
    def put(self, endpoint: str, **kwargs) -> requests.Response:
        """发送PUT请求"""
        return self.request('PUT', endpoint, **kwargs)
    
    def delete(self, endpoint: str, **kwargs) -> requests.Response:
        """发送DELETE请求"""
        return self.request('DELETE', endpoint, **kwargs)
    
    def register_user(self, user_data: Dict[str, str]) -> requests.Response:
        """注册用户"""
        return self.post(API_ENDPOINTS['auth']['register'], json=user_data)
    
    def login_user(self, credentials: Dict[str, str]) -> requests.Response:
        """用户登录"""
        response = self.post(API_ENDPOINTS['auth']['login'], json=credentials)
        if response.status_code == 200:
            data = response.json()
            if 'access_token' in data:
                self.set_auth_token(data['access_token'])
            if 'refresh_token' in data:
                self.refresh_token = data['refresh_token']
        return response
    
    def logout_user(self) -> requests.Response:
        """用户登出"""
        response = self.post(API_ENDPOINTS['auth']['logout'])
        if response.status_code in [200, 204]:
            self.clear_auth()
        return response


class TestDataManager:
    """测试数据管理器"""
    
    def __init__(self, api_client: APIClient):
        self.api_client = api_client
        self.created_items = {
            'users': [],
            'todos': [],
            'achievements': [],
            'plans': []
        }
    
    def create_test_user(self, user_data: Dict[str, str] = None) -> Dict[str, Any]:
        """创建测试用户"""
        if user_data is None:
            user_data = TestConfig.TEST_USER.copy()
            # 添加随机后缀避免冲突
            import random
            suffix = random.randint(1000, 9999)
            user_data['username'] = f"{user_data['username']}_{suffix}"
            user_data['email'] = f"test_{suffix}@example.com"
        
        response = self.api_client.register_user(user_data)
        if response.status_code == 201:
            self.created_items['users'].append(user_data)
        return response.json() if response.status_code == 201 else None
    
    def create_test_todo(self, todo_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建测试Todo项"""
        if todo_data is None:
            todo_data = TEST_DATA_TEMPLATES['todo'].copy()
        
        response = self.api_client.post(API_ENDPOINTS['todo']['create'], json=todo_data)
        if response.status_code == 201:
            item = response.json()
            self.created_items['todos'].append(item)
            return item
        return None
    
    def create_test_achievement(self, achievement_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建测试成就"""
        if achievement_data is None:
            achievement_data = TEST_DATA_TEMPLATES['achievement'].copy()
        
        response = self.api_client.post(API_ENDPOINTS['achievements']['create'], json=achievement_data)
        if response.status_code == 201:
            item = response.json()
            self.created_items['achievements'].append(item)
            return item
        return None
    
    def create_test_plan(self, plan_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建测试计划"""
        if plan_data is None:
            plan_data = TEST_DATA_TEMPLATES['plan'].copy()
        
        response = self.api_client.post(API_ENDPOINTS['plans']['create'], json=plan_data)
        if response.status_code == 201:
            item = response.json()
            self.created_items['plans'].append(item)
            return item
        return None
    
    def cleanup(self):
        """清理创建的测试数据"""
        # 清理计划
        for plan in self.created_items['plans']:
            if 'id' in plan:
                self.api_client.delete(API_ENDPOINTS['plans']['delete'].format(id=plan['id']))
        
        # 清理成就
        for achievement in self.created_items['achievements']:
            if 'id' in achievement:
                self.api_client.delete(API_ENDPOINTS['achievements']['delete'].format(id=achievement['id']))
        
        # 清理Todo项
        for todo in self.created_items['todos']:
            if 'id' in todo:
                self.api_client.delete(API_ENDPOINTS['todo']['delete'].format(id=todo['id']))
        
        # 重置记录
        self.created_items = {
            'users': [],
            'todos': [],
            'achievements': [],
            'plans': []
        }


class ProcessManager:
    """进程管理器，用于启动和停止服务"""
    
    def __init__(self):
        self.processes = {}
    
    def start_backend(self, cwd: str = None) -> subprocess.Popen:
        """启动后端服务"""
        if cwd is None:
            cwd = PROJECT_ROOT / 'backend'
        
        # 设置环境变量
        env = os.environ.copy()
        env.update({
            'FLASK_CONFIG': 'testing',
            'SECRET_KEY': TestConfig.SECRET_KEY,
            'JWT_SECRET_KEY': TestConfig.JWT_SECRET_KEY
        })
        
        # 启动Flask应用
        process = subprocess.Popen(
            [sys.executable, 'run.py'],
            cwd=cwd,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        self.processes['backend'] = process
        return process
    
    def start_frontend(self, cwd: str = None) -> subprocess.Popen:
        """启动前端服务"""
        if cwd is None:
            cwd = PROJECT_ROOT / 'svelte@latest'
        
        # 启动前端开发服务器
        process = subprocess.Popen(
            ['npm', 'run', 'dev'],
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        self.processes['frontend'] = process
        return process
    
    def stop_all(self):
        """停止所有进程"""
        for name, process in self.processes.items():
            if process and process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()
        self.processes.clear()
    
    def wait_for_service(self, url: str, timeout: int = 60) -> bool:
        """等待服务启动"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code < 500:
                    return True
            except requests.RequestException:
                pass
            time.sleep(1)
        return False


@contextmanager
def test_environment():
    """测试环境上下文管理器"""
    process_manager = ProcessManager()
    api_client = APIClient()
    data_manager = TestDataManager(api_client)
    
    try:
        # 启动服务
        backend_process = process_manager.start_backend()
        
        # 等待后端服务启动
        if not process_manager.wait_for_service(TestConfig.API_BASE_URL):
            raise RuntimeError("Backend service failed to start")
        
        yield {
            'api_client': api_client,
            'data_manager': data_manager,
            'process_manager': process_manager
        }
    
    finally:
        # 清理测试数据
        if TestConfig.CLEANUP_AFTER_TESTS:
            data_manager.cleanup()
        
        # 停止所有服务
        process_manager.stop_all()


def assert_response_success(response: requests.Response, expected_status: int = 200):
    """断言响应成功"""
    assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}: {response.text}"


def assert_response_error(response: requests.Response, expected_status: int = 400):
    """断言响应错误"""
    assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}: {response.text}"


def assert_json_structure(data: Dict[str, Any], required_fields: List[str]):
    """断言JSON结构包含必需字段"""
    for field in required_fields:
        assert field in data, f"Required field '{field}' not found in response"
