#!/usr/bin/env python3
"""
运行所有测试的主脚本
支持不同的测试模式和配置选项
"""

import os
import sys
import argparse
import subprocess
import time
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from test.config.test_config import TestConfig, TEST_ENV
from test.utils.test_helpers import ProcessManager


class TestRunner:
    """测试运行器"""
    
    def __init__(self, args):
        self.args = args
        self.process_manager = ProcessManager()
        self.test_results = {}
        
    def setup_environment(self):
        """设置测试环境"""
        print("🔧 Setting up test environment...")
        
        # 设置环境变量
        for key, value in TEST_ENV.items():
            os.environ[key] = str(value)
        
        # 创建测试目录
        test_dirs = [
            PROJECT_ROOT / 'test' / 'reports',
            PROJECT_ROOT / 'test' / 'screenshots',
            PROJECT_ROOT / 'test' / 'logs'
        ]
        
        for test_dir in test_dirs:
            test_dir.mkdir(parents=True, exist_ok=True)
        
        print("✅ Test environment setup complete")
    
    def install_dependencies(self):
        """安装测试依赖"""
        if self.args.skip_install:
            print("⏭️  Skipping dependency installation")
            return
        
        print("📦 Installing test dependencies...")
        
        # 安装Python依赖
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 
                str(PROJECT_ROOT / 'test' / 'requirements.txt')
            ], check=True, capture_output=True)
            print("✅ Python dependencies installed")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Python dependencies: {e}")
            if not self.args.continue_on_error:
                sys.exit(1)
        
        # 安装Node.js依赖（如果需要）
        if self.args.include_frontend or self.args.test_type in ['all', 'integration', 'e2e']:
            test_package_json = PROJECT_ROOT / 'test' / 'package.json'
            if test_package_json.exists():
                try:
                    subprocess.run([
                        'npm', 'install'
                    ], cwd=PROJECT_ROOT / 'test', check=True, capture_output=True)
                    print("✅ Node.js test dependencies installed")
                except subprocess.CalledProcessError as e:
                    print(f"❌ Failed to install Node.js dependencies: {e}")
                    if not self.args.continue_on_error:
                        sys.exit(1)
    
    def run_backend_tests(self) -> bool:
        """运行后端测试"""
        print("🧪 Running backend API tests...")
        
        test_dir = PROJECT_ROOT / 'test' / 'backend'
        if not test_dir.exists():
            print("⚠️  Backend test directory not found")
            return False
        
        # 构建pytest命令
        cmd = [
            sys.executable, '-m', 'pytest',
            str(test_dir),
            '-v',
            '--tb=short'
        ]
        
        if self.args.coverage:
            cmd.extend(['--cov=backend', '--cov-report=html', '--cov-report=term'])
        
        if self.args.parallel:
            cmd.extend(['-n', str(self.args.workers)])
        
        if self.args.output_file:
            cmd.extend(['--html=' + str(PROJECT_ROOT / 'test' / 'reports' / 'backend_report.html')])
        
        try:
            result = subprocess.run(cmd, cwd=PROJECT_ROOT, capture_output=False)
            success = result.returncode == 0
            self.test_results['backend'] = success
            
            if success:
                print("✅ Backend tests passed")
            else:
                print("❌ Backend tests failed")
            
            return success
            
        except Exception as e:
            print(f"❌ Error running backend tests: {e}")
            self.test_results['backend'] = False
            return False
    
    def run_integration_tests(self) -> bool:
        """运行集成测试"""
        print("🔗 Running integration tests...")
        
        test_dir = PROJECT_ROOT / 'test' / 'integration'
        if not test_dir.exists():
            print("⚠️  Integration test directory not found")
            return False
        
        # 构建pytest命令
        cmd = [
            sys.executable, '-m', 'pytest',
            str(test_dir),
            '-v',
            '--tb=short'
        ]
        
        if self.args.output_file:
            cmd.extend(['--html=' + str(PROJECT_ROOT / 'test' / 'reports' / 'integration_report.html')])
        
        try:
            result = subprocess.run(cmd, cwd=PROJECT_ROOT, capture_output=False)
            success = result.returncode == 0
            self.test_results['integration'] = success
            
            if success:
                print("✅ Integration tests passed")
            else:
                print("❌ Integration tests failed")
            
            return success
            
        except Exception as e:
            print(f"❌ Error running integration tests: {e}")
            self.test_results['integration'] = False
            return False
    
    def run_frontend_tests(self) -> bool:
        """运行前端测试"""
        print("🎨 Running frontend tests...")
        
        # 检查是否有npm
        try:
            subprocess.run(['npm', '--version'], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  npm not available, skipping frontend tests")
            return True
        
        test_package_json = PROJECT_ROOT / 'test' / 'package.json'
        if not test_package_json.exists():
            print("⚠️  Frontend test configuration not found")
            return False
        
        try:
            result = subprocess.run([
                'npm', 'test'
            ], cwd=PROJECT_ROOT / 'test', capture_output=False)
            
            success = result.returncode == 0
            self.test_results['frontend'] = success
            
            if success:
                print("✅ Frontend tests passed")
            else:
                print("❌ Frontend tests failed")
            
            return success
            
        except Exception as e:
            print(f"❌ Error running frontend tests: {e}")
            self.test_results['frontend'] = False
            return False
    
    def run_e2e_tests(self) -> bool:
        """运行端到端测试"""
        print("🌐 Running end-to-end tests...")
        
        # 检查是否有必要的工具
        try:
            subprocess.run(['npm', '--version'], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  npm not available, skipping e2e tests")
            return True
        
        test_package_json = PROJECT_ROOT / 'test' / 'package.json'
        if not test_package_json.exists():
            print("⚠️  E2E test configuration not found")
            return False
        
        try:
            result = subprocess.run([
                'npm', 'run', 'test:e2e'
            ], cwd=PROJECT_ROOT / 'test', capture_output=False)
            
            success = result.returncode == 0
            self.test_results['e2e'] = success
            
            if success:
                print("✅ E2E tests passed")
            else:
                print("❌ E2E tests failed")
            
            return success
            
        except Exception as e:
            print(f"❌ Error running e2e tests: {e}")
            self.test_results['e2e'] = False
            return False
    
    def run_tests(self):
        """运行测试"""
        print(f"🚀 Starting test run (type: {self.args.test_type})")
        start_time = time.time()
        
        all_passed = True
        
        if self.args.test_type in ['all', 'backend']:
            if not self.run_backend_tests() and not self.args.continue_on_error:
                all_passed = False
        
        if self.args.test_type in ['all', 'integration']:
            if not self.run_integration_tests() and not self.args.continue_on_error:
                all_passed = False
        
        if self.args.test_type in ['all', 'frontend'] or self.args.include_frontend:
            if not self.run_frontend_tests() and not self.args.continue_on_error:
                all_passed = False
        
        if self.args.test_type in ['all', 'e2e']:
            if not self.run_e2e_tests() and not self.args.continue_on_error:
                all_passed = False
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试结果摘要
        self.print_summary(duration, all_passed)
        
        return all_passed
    
    def print_summary(self, duration: float, all_passed: bool):
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print("📊 TEST SUMMARY")
        print("="*60)
        
        for test_type, passed in self.test_results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"{test_type.upper():<15} {status}")
        
        print(f"\n⏱️  Total duration: {duration:.2f} seconds")
        
        if all_passed:
            print("🎉 All tests passed!")
        else:
            print("💥 Some tests failed!")
        
        print("="*60)
    
    def cleanup(self):
        """清理测试环境"""
        print("🧹 Cleaning up test environment...")
        self.process_manager.stop_all()
        print("✅ Cleanup complete")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Run YourWorkspace test suite')
    
    parser.add_argument(
        '--test-type', 
        choices=['all', 'backend', 'integration', 'frontend', 'e2e'],
        default='all',
        help='Type of tests to run (default: all)'
    )
    
    parser.add_argument(
        '--skip-install',
        action='store_true',
        help='Skip dependency installation'
    )
    
    parser.add_argument(
        '--include-frontend',
        action='store_true',
        help='Include frontend tests even if not specified in test-type'
    )
    
    parser.add_argument(
        '--coverage',
        action='store_true',
        help='Generate coverage report for backend tests'
    )
    
    parser.add_argument(
        '--parallel',
        action='store_true',
        help='Run tests in parallel'
    )
    
    parser.add_argument(
        '--workers',
        type=int,
        default=4,
        help='Number of parallel workers (default: 4)'
    )
    
    parser.add_argument(
        '--output-file',
        action='store_true',
        help='Generate HTML test reports'
    )
    
    parser.add_argument(
        '--continue-on-error',
        action='store_true',
        help='Continue running tests even if some fail'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Verbose output'
    )
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = TestRunner(args)
    
    try:
        # 设置环境
        runner.setup_environment()
        
        # 安装依赖
        runner.install_dependencies()
        
        # 运行测试
        success = runner.run_tests()
        
        # 退出码
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️  Test run interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
    finally:
        runner.cleanup()


if __name__ == '__main__':
    main()
