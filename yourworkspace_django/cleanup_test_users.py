#!/usr/bin/env python3
"""
Clean up test users from the database
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yourworkspace_django.settings')
django.setup()

from authentication.models import User

def cleanup_test_users():
    """Remove test users from database"""
    
    # List of test emails and usernames to remove
    test_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ]
    
    test_usernames = [
        'testuser123',
        'testuser456',
        'testuser',
    ]
    
    print("Cleaning up test users...")
    
    # Remove users by email
    for email in test_emails:
        users = User.objects.filter(email=email)
        if users.exists():
            count = users.count()
            users.delete()
            print(f"Deleted {count} user(s) with email: {email}")
        else:
            print(f"No users found with email: {email}")
    
    # Remove users by username
    for username in test_usernames:
        users = User.objects.filter(username=username)
        if users.exists():
            count = users.count()
            users.delete()
            print(f"Deleted {count} user(s) with username: {username}")
        else:
            print(f"No users found with username: {username}")
    
    print("Cleanup completed!")
    
    # Show remaining users
    all_users = User.objects.all()
    print(f"\nRemaining users in database: {all_users.count()}")
    for user in all_users:
        print(f"  - {user.username} ({user.email})")

if __name__ == "__main__":
    cleanup_test_users()
