from django.contrib import admin
from .models import TodoItem


@admin.register(TodoItem)
class TodoItemAdmin(admin.ModelAdmin):
    """待办事项管理界面"""

    # 列表显示字段
    list_display = ['title', 'user', 'status', 'priority', 'is_current_focus', 'due_date', 'created_at']
    list_filter = ['status', 'priority', 'is_current_focus', 'due_date', 'created_at']
    search_fields = ['title', 'description', 'user__username', 'user__email']
    list_editable = ['status', 'priority', 'is_current_focus']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'

    # 详细页面字段组织
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'title', 'description')
        }),
        ('状态和优先级', {
            'fields': ('status', 'priority', 'is_current_focus')
        }),
        ('时间信息', {
            'fields': ('due_date', 'completed_at'),
        }),
        ('时间戳', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    # 只读字段
    readonly_fields = ['created_at', 'updated_at', 'completed_at']

    # 原始ID字段
    raw_id_fields = ['user']

    # 自定义操作
    actions = ['mark_as_completed', 'mark_as_pending', 'set_high_priority']

    def mark_as_completed(self, request, queryset):
        """批量标记为完成"""
        from django.utils import timezone
        updated = queryset.update(status='completed', completed_at=timezone.now())
        self.message_user(request, f'{updated} 个待办事项已标记为完成')
    mark_as_completed.short_description = '标记为完成'

    def mark_as_pending(self, request, queryset):
        """批量标记为待处理"""
        updated = queryset.update(status='pending', completed_at=None)
        self.message_user(request, f'{updated} 个待办事项已标记为待处理')
    mark_as_pending.short_description = '标记为待处理'

    def set_high_priority(self, request, queryset):
        """批量设置为高优先级"""
        updated = queryset.update(priority='high')
        self.message_user(request, f'{updated} 个待办事项已设置为高优先级')
    set_high_priority.short_description = '设置为高优先级'
