"""
Todo views for Django REST Framework
Migrated from Flask todo_bp.py
"""

from rest_framework import status, viewsets
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone

from .models import TodoItem
from .serializers import (
    TodoItemSerializer,
    TodoItemCreateSerializer,
    TodoItemUpdateSerializer
)


@api_view(['GET'])
@permission_classes([AllowAny])
def ping_todo(request):
    """Simple test route - migrated from Flask ping endpoint"""
    return Response({"message": "Todo API is alive!"}, status=status.HTTP_200_OK)


class TodoItemViewSet(viewsets.ModelViewSet):
    """
    待办事项视图集
    Migrated from Flask todo_bp endpoints
    """
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """获取当前用户的待办事项，按焦点和创建时间排序"""
        return TodoItem.objects.filter(user=self.request.user).order_by(
            '-is_current_focus', '-created_at'
        )

    def get_serializer_class(self):
        """根据操作选择序列化器"""
        if self.action == 'create':
            return TodoItemCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return TodoItemUpdateSerializer
        return TodoItemSerializer

    def list(self, request):
        """
        获取所有待办事项
        GET /api/v1/todo/todos/
        """
        queryset = self.get_queryset()
        serializer = TodoItemSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def create(self, request):
        """
        创建新的待办事项
        POST /api/v1/todo/todos/
        """
        serializer = TodoItemCreateSerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            todo = serializer.save()
            response_serializer = TodoItemSerializer(todo)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response({
            'error': 'Todo creation failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, pk=None):
        """
        获取单个待办事项
        GET /api/v1/todo/todos/{id}/
        """
        todo = get_object_or_404(TodoItem, pk=pk, user=request.user)
        serializer = TodoItemSerializer(todo)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def update(self, request, pk=None):
        """
        完整更新待办事项
        PUT /api/v1/todo/todos/{id}/
        """
        todo = get_object_or_404(TodoItem, pk=pk, user=request.user)
        serializer = TodoItemUpdateSerializer(todo, data=request.data, context={'request': request})

        if serializer.is_valid():
            updated_todo = serializer.save()
            response_serializer = TodoItemSerializer(updated_todo)
            return Response(response_serializer.data, status=status.HTTP_200_OK)

        return Response({
            'error': 'Todo update failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def partial_update(self, request, pk=None):
        """
        部分更新待办事项
        PATCH /api/v1/todo/todos/{id}/
        """
        todo = get_object_or_404(TodoItem, pk=pk, user=request.user)
        serializer = TodoItemUpdateSerializer(todo, data=request.data, partial=True, context={'request': request})

        if serializer.is_valid():
            updated_todo = serializer.save()
            response_serializer = TodoItemSerializer(updated_todo)
            return Response(response_serializer.data, status=status.HTTP_200_OK)

        return Response({
            'error': 'Todo update failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk=None):
        """
        删除待办事项
        DELETE /api/v1/todo/todos/{id}/
        """
        todo = get_object_or_404(TodoItem, pk=pk, user=request.user)
        todo.delete()
        return Response({
            'message': 'Todo deleted successfully'
        }, status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['post'])
    def mark_completed(self, request, pk=None):
        """
        标记待办事项为完成
        POST /api/v1/todo/todos/{id}/mark_completed/
        """
        todo = get_object_or_404(TodoItem, pk=pk, user=request.user)
        todo.status = 'completed'
        todo.completed_at = timezone.now()
        todo.save()

        serializer = TodoItemSerializer(todo)
        return Response({
            'message': 'Todo marked as completed',
            'todo': serializer.data
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def set_focus(self, request, pk=None):
        """
        设置/取消当前焦点
        POST /api/v1/todo/todos/{id}/set_focus/
        """
        todo = get_object_or_404(TodoItem, pk=pk, user=request.user)

        # 获取请求中的焦点状态，默认为True
        is_focus = request.data.get('is_current_focus', True)

        todo.is_current_focus = is_focus
        todo.save()

        serializer = TodoItemSerializer(todo)
        return Response({
            'message': f'Todo focus {"set" if is_focus else "removed"}',
            'todo': serializer.data
        }, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def current_focus(self, request):
        """
        获取当前焦点的待办事项
        GET /api/v1/todo/todos/current_focus/
        """
        focus_todos = TodoItem.objects.filter(
            user=request.user,
            is_current_focus=True
        ).order_by('-created_at')

        serializer = TodoItemSerializer(focus_todos, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
