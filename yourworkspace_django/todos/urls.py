"""
Todo URL configuration
Migrated from Flask todo_bp routes
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'todos'

# Create a router and register our viewset
router = DefaultRouter()
router.register(r'todos', views.TodoItemViewSet, basename='todoitem')

urlpatterns = [
    # Health check
    path('ping/', views.ping_todo, name='ping'),
    
    # Include router URLs
    path('', include(router.urls)),
]
