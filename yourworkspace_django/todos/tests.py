"""
Todo tests for Django application
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework.authtoken.models import Token
from django.utils import timezone
from datetime import date, timedelta

from .models import TodoItem

User = get_user_model()


class TodoItemModelTest(TestCase):
    """待办事项模型测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_todo_item(self):
        """测试创建待办事项"""
        todo = TodoItem.objects.create(
            user=self.user,
            title='Test Todo',
            description='Test Description',
            priority='high'
        )

        self.assertEqual(todo.title, 'Test Todo')
        self.assertEqual(todo.description, 'Test Description')
        self.assertEqual(todo.priority, 'high')
        self.assertEqual(todo.status, 'pending')
        self.assertFalse(todo.is_current_focus)
        self.assertIsNone(todo.completed_at)

    def test_todo_str_representation(self):
        """测试待办事项字符串表示"""
        todo = TodoItem.objects.create(
            user=self.user,
            title='Test Todo Item'
        )
        self.assertEqual(str(todo), 'Test Todo Item')

    def test_todo_completion(self):
        """测试待办事项完成功能"""
        todo = TodoItem.objects.create(
            user=self.user,
            title='Test Todo'
        )

        # 标记为完成
        todo.status = 'completed'
        todo.save()

        self.assertEqual(todo.status, 'completed')
        self.assertIsNotNone(todo.completed_at)

    def test_todo_to_dict(self):
        """测试待办事项字典转换"""
        todo = TodoItem.objects.create(
            user=self.user,
            title='Test Todo',
            description='Test Description'
        )

        todo_dict = todo.to_dict()
        self.assertIn('id', todo_dict)
        self.assertIn('title', todo_dict)
        self.assertIn('description', todo_dict)
        self.assertIn('status', todo_dict)
        self.assertIn('priority', todo_dict)


class TodoItemAPITest(APITestCase):
    """待办事项API测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        self.todos_url = '/api/v1/todo/todos/'

    def test_create_todo(self):
        """测试创建待办事项API"""
        data = {
            'title': 'New Todo',
            'description': 'New Description',
            'priority': 'high'
        }

        response = self.client.post(self.todos_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        todo_data = response.json()
        self.assertEqual(todo_data['title'], 'New Todo')
        self.assertEqual(todo_data['priority'], 'high')
        self.assertEqual(todo_data['status'], 'pending')

    def test_list_todos(self):
        """测试获取待办事项列表API"""
        # 创建测试数据
        TodoItem.objects.create(user=self.user, title='Todo 1')
        TodoItem.objects.create(user=self.user, title='Todo 2')

        response = self.client.get(self.todos_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        todos = response.json()
        self.assertEqual(len(todos), 2)

    def test_update_todo(self):
        """测试更新待办事项API"""
        todo = TodoItem.objects.create(user=self.user, title='Original Title')

        data = {
            'title': 'Updated Title',
            'status': 'in_progress'
        }

        response = self.client.patch(f'{self.todos_url}{todo.id}/', data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        updated_todo = response.json()
        self.assertEqual(updated_todo['title'], 'Updated Title')
        self.assertEqual(updated_todo['status'], 'in_progress')

    def test_delete_todo(self):
        """测试删除待办事项API"""
        todo = TodoItem.objects.create(user=self.user, title='To Delete')

        response = self.client.delete(f'{self.todos_url}{todo.id}/')
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 验证已删除
        self.assertFalse(TodoItem.objects.filter(id=todo.id).exists())

    def test_mark_completed_action(self):
        """测试标记完成动作"""
        todo = TodoItem.objects.create(user=self.user, title='To Complete')

        response = self.client.post(f'{self.todos_url}{todo.id}/mark_completed/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 刷新对象
        todo.refresh_from_db()
        self.assertEqual(todo.status, 'completed')
        self.assertIsNotNone(todo.completed_at)

    def test_user_isolation(self):
        """测试用户数据隔离"""
        # 创建另一个用户
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='otherpass123'
        )

        # 为另一个用户创建待办事项
        TodoItem.objects.create(user=other_user, title='Other User Todo')

        # 当前用户应该看不到其他用户的待办事项
        response = self.client.get(self.todos_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        todos = response.json()
        self.assertEqual(len(todos), 0)  # 应该为空
