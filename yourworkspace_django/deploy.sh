#!/bin/bash

# YourWorkspace Django Deployment Script
# This script automates the deployment process

set -e  # Exit on any error

echo "🚀 Starting YourWorkspace Django deployment..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ Error: .env file not found. Please copy .env.example to .env and configure it."
    exit 1
fi

# Load environment variables
source .env

# Check required environment variables
required_vars=("SECRET_KEY" "DATABASE_URL" "ALLOWED_HOSTS")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Error: Required environment variable $var is not set."
        exit 1
    fi
done

echo "✅ Environment variables validated"

# Create necessary directories
mkdir -p logs
mkdir -p media
mkdir -p staticfiles
mkdir -p ssl

echo "✅ Directories created"

# Build and start services
echo "🔨 Building Docker images..."
docker-compose build

echo "🗄️ Starting database and Redis..."
docker-compose up -d db redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Run database migrations
echo "🔄 Running database migrations..."
docker-compose run --rm web python manage.py migrate

# Create superuser if it doesn't exist
echo "👤 Creating superuser (if needed)..."
docker-compose run --rm web python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
"

# Collect static files
echo "📦 Collecting static files..."
docker-compose run --rm web python manage.py collectstatic --noinput

# Start all services
echo "🚀 Starting all services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 15

# Health check
echo "🏥 Performing health check..."
if curl -f http://localhost:8000/api/v1/ > /dev/null 2>&1; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    echo "📋 Checking service logs..."
    docker-compose logs web
    exit 1
fi

echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Service Information:"
echo "   - Django API: http://localhost:8000"
echo "   - Admin Panel: http://localhost:8000/admin/"
echo "   - Database: PostgreSQL on port 5432"
echo "   - Redis: Redis on port 6379"
echo ""
echo "🔧 Useful commands:"
echo "   - View logs: docker-compose logs -f"
echo "   - Stop services: docker-compose down"
echo "   - Restart services: docker-compose restart"
echo "   - Run migrations: docker-compose run --rm web python manage.py migrate"
echo "   - Create superuser: docker-compose run --rm web python manage.py createsuperuser"
echo ""
echo "⚠️  Remember to:"
echo "   - Configure SSL certificates for production"
echo "   - Set up proper backup procedures"
echo "   - Configure monitoring and alerting"
echo "   - Review security settings"
