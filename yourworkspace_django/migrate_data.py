#!/usr/bin/env python
"""
数据迁移脚本：从Flask SQLite数据库迁移数据到Django数据库
"""

import os
import sys
import sqlite3
import json
from datetime import datetime, date
from pathlib import Path

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yourworkspace_django.settings')

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import django
django.setup()

from django.contrib.auth.hashers import make_password
from authentication.models import User
from user_profiles.models import UserProfile
from todos.models import TodoItem
from achievements.models import Achievement
from plans.models import FuturePlan


class DataMigrator:
    """数据迁移器"""
    
    def __init__(self, flask_db_path):
        self.flask_db_path = flask_db_path
        self.flask_conn = None
        self.user_mapping = {}  # Flask user_id -> Django user_id 映射
        
    def connect_flask_db(self):
        """连接Flask数据库"""
        if not os.path.exists(self.flask_db_path):
            raise FileNotFoundError(f"Flask数据库文件不存在: {self.flask_db_path}")
        
        self.flask_conn = sqlite3.connect(self.flask_db_path)
        self.flask_conn.row_factory = sqlite3.Row  # 使用字典式访问
        print(f"已连接到Flask数据库: {self.flask_db_path}")
    
    def close_flask_db(self):
        """关闭Flask数据库连接"""
        if self.flask_conn:
            self.flask_conn.close()
            print("已关闭Flask数据库连接")
    
    def migrate_users(self):
        """迁移用户数据"""
        print("开始迁移用户数据...")
        
        cursor = self.flask_conn.cursor()
        cursor.execute("SELECT * FROM users")
        flask_users = cursor.fetchall()
        
        migrated_count = 0
        for flask_user in flask_users:
            try:
                # 检查用户是否已存在
                if User.objects.filter(email=flask_user['email']).exists():
                    print(f"用户 {flask_user['email']} 已存在，跳过")
                    existing_user = User.objects.get(email=flask_user['email'])
                    self.user_mapping[flask_user['id']] = existing_user.id
                    continue
                
                # 创建Django用户
                django_user = User.objects.create(
                    username=flask_user['username'],
                    email=flask_user['email'],
                    password=flask_user['password_hash'],  # 直接使用Flask的密码哈希
                    date_joined=self.parse_datetime(flask_user['created_at']) if 'created_at' in flask_user.keys() else None,
                    is_active=True
                )

                # 更新时间戳
                if 'updated_at' in flask_user.keys() and flask_user['updated_at']:
                    django_user.updated_at = self.parse_datetime(flask_user['updated_at'])
                    django_user.save(update_fields=['updated_at'])
                
                self.user_mapping[flask_user['id']] = django_user.id
                migrated_count += 1
                print(f"迁移用户: {flask_user['username']} -> ID: {django_user.id}")
                
            except Exception as e:
                print(f"迁移用户 {flask_user['username']} 失败: {e}")
        
        print(f"用户迁移完成，共迁移 {migrated_count} 个用户")
        return migrated_count
    
    def migrate_user_profiles(self):
        """迁移用户档案数据"""
        print("开始迁移用户档案数据...")
        
        cursor = self.flask_conn.cursor()
        cursor.execute("SELECT * FROM user_profiles")
        flask_profiles = cursor.fetchall()
        
        migrated_count = 0
        for flask_profile in flask_profiles:
            try:
                flask_user_id = flask_profile['id']  # user_profiles表中id就是user_id
                
                if flask_user_id not in self.user_mapping:
                    print(f"找不到对应的用户ID {flask_user_id}，跳过档案迁移")
                    continue
                
                django_user_id = self.user_mapping[flask_user_id]
                django_user = User.objects.get(id=django_user_id)
                
                # 检查是否已存在档案
                if hasattr(django_user, 'profile'):
                    print(f"用户 {django_user.username} 的档案已存在，更新数据")
                    profile = django_user.profile
                else:
                    profile = UserProfile(user=django_user)
                
                # 更新档案数据
                profile.professional_title = flask_profile['professional_title'] if 'professional_title' in flask_profile.keys() else None
                profile.one_liner_bio = flask_profile['one_liner_bio'] if 'one_liner_bio' in flask_profile.keys() else None
                profile.skill = flask_profile['skill'] if 'skill' in flask_profile.keys() else None
                profile.summary = flask_profile['summary'] if 'summary' in flask_profile.keys() else None

                if 'created_at' in flask_profile.keys() and flask_profile['created_at']:
                    profile.created_at = self.parse_datetime(flask_profile['created_at'])
                if 'updated_at' in flask_profile.keys() and flask_profile['updated_at']:
                    profile.updated_at = self.parse_datetime(flask_profile['updated_at'])
                
                profile.save()
                migrated_count += 1
                print(f"迁移用户档案: {django_user.username}")
                
            except Exception as e:
                print(f"迁移用户档案失败: {e}")
        
        print(f"用户档案迁移完成，共迁移 {migrated_count} 个档案")
        return migrated_count
    
    def migrate_todos(self):
        """迁移待办事项数据"""
        print("开始迁移待办事项数据...")
        
        cursor = self.flask_conn.cursor()
        cursor.execute("SELECT * FROM todo_items")
        flask_todos = cursor.fetchall()
        
        migrated_count = 0
        for flask_todo in flask_todos:
            try:
                flask_user_id = flask_todo['user_id']
                
                if flask_user_id not in self.user_mapping:
                    print(f"找不到对应的用户ID {flask_user_id}，跳过待办事项迁移")
                    continue
                
                django_user_id = self.user_mapping[flask_user_id]
                django_user = User.objects.get(id=django_user_id)
                
                # 创建待办事项
                todo = TodoItem.objects.create(
                    user=django_user,
                    title=flask_todo['title'],
                    description=flask_todo['description'] if 'description' in flask_todo.keys() and flask_todo['description'] else '',
                    due_date=self.parse_date(flask_todo['due_date']) if 'due_date' in flask_todo.keys() else None,
                    status=flask_todo['status'] if 'status' in flask_todo.keys() else 'pending',
                    priority=flask_todo['priority'] if 'priority' in flask_todo.keys() else 'medium',
                    is_current_focus=bool(flask_todo['is_current_focus']) if 'is_current_focus' in flask_todo.keys() else False,
                    completed_at=self.parse_datetime(flask_todo['completed_at']) if 'completed_at' in flask_todo.keys() else None,
                    created_at=self.parse_datetime(flask_todo['created_at']) if 'created_at' in flask_todo.keys() else None,
                    updated_at=self.parse_datetime(flask_todo['updated_at']) if 'updated_at' in flask_todo.keys() else None
                )
                
                migrated_count += 1
                print(f"迁移待办事项: {flask_todo['title'][:30]}...")
                
            except Exception as e:
                print(f"迁移待办事项失败: {e}")
        
        print(f"待办事项迁移完成，共迁移 {migrated_count} 个事项")
        return migrated_count
    
    def migrate_achievements(self):
        """迁移成就数据"""
        print("开始迁移成就数据...")
        
        cursor = self.flask_conn.cursor()
        cursor.execute("SELECT * FROM achievements")
        flask_achievements = cursor.fetchall()
        
        migrated_count = 0
        for flask_achievement in flask_achievements:
            try:
                flask_user_id = flask_achievement['user_id']
                
                if flask_user_id not in self.user_mapping:
                    print(f"找不到对应的用户ID {flask_user_id}，跳过成就迁移")
                    continue
                
                django_user_id = self.user_mapping[flask_user_id]
                django_user = User.objects.get(id=django_user_id)
                
                # 处理技能JSON数据
                core_skills = []
                if flask_achievement.get('core_skills_json'):
                    try:
                        if isinstance(flask_achievement['core_skills_json'], str):
                            core_skills = json.loads(flask_achievement['core_skills_json'])
                        else:
                            core_skills = flask_achievement['core_skills_json']
                    except (json.JSONDecodeError, TypeError):
                        core_skills = []
                
                # 创建成就
                achievement = Achievement.objects.create(
                    user=django_user,
                    title=flask_achievement['title'],
                    description=flask_achievement.get('description', ''),
                    quantifiable_results=flask_achievement.get('quantifiable_results', ''),
                    core_skills_json=core_skills,
                    date_achieved=self.parse_date(flask_achievement.get('date_achieved')),
                    created_at=self.parse_datetime(flask_achievement.get('created_at')),
                    updated_at=self.parse_datetime(flask_achievement.get('updated_at'))
                )
                
                migrated_count += 1
                print(f"迁移成就: {flask_achievement['title'][:30]}...")
                
            except Exception as e:
                print(f"迁移成就失败: {e}")
        
        print(f"成就迁移完成，共迁移 {migrated_count} 个成就")
        return migrated_count
    
    def migrate_plans(self):
        """迁移计划数据"""
        print("开始迁移计划数据...")
        
        cursor = self.flask_conn.cursor()
        cursor.execute("SELECT * FROM future_plans")
        flask_plans = cursor.fetchall()
        
        migrated_count = 0
        for flask_plan in flask_plans:
            try:
                flask_user_id = flask_plan['user_id']
                
                if flask_user_id not in self.user_mapping:
                    print(f"找不到对应的用户ID {flask_user_id}，跳过计划迁移")
                    continue
                
                django_user_id = self.user_mapping[flask_user_id]
                django_user = User.objects.get(id=django_user_id)
                
                # 创建计划
                plan = FuturePlan.objects.create(
                    user=django_user,
                    goal_type=flask_plan.get('goal_type', ''),
                    title=flask_plan['title'],
                    description=flask_plan['description'],
                    target_date=self.parse_date(flask_plan.get('target_date')),
                    status=flask_plan.get('status', 'active'),
                    created_at=self.parse_datetime(flask_plan.get('created_at')),
                    updated_at=self.parse_datetime(flask_plan.get('updated_at'))
                )
                
                migrated_count += 1
                print(f"迁移计划: {flask_plan['title'][:30]}...")
                
            except Exception as e:
                print(f"迁移计划失败: {e}")
        
        print(f"计划迁移完成，共迁移 {migrated_count} 个计划")
        return migrated_count
    
    def parse_datetime(self, dt_str):
        """解析日期时间字符串"""
        if not dt_str:
            return None
        
        try:
            # 尝试不同的日期时间格式
            formats = [
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(dt_str, fmt)
                except ValueError:
                    continue
            
            print(f"无法解析日期时间: {dt_str}")
            return None
            
        except Exception as e:
            print(f"解析日期时间出错: {e}")
            return None
    
    def parse_date(self, date_str):
        """解析日期字符串"""
        if not date_str:
            return None
        
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            try:
                return datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%S').date()
            except ValueError:
                print(f"无法解析日期: {date_str}")
                return None
    
    def run_migration(self):
        """运行完整的数据迁移"""
        print("开始数据迁移...")
        
        try:
            self.connect_flask_db()
            
            # 按顺序迁移数据
            user_count = self.migrate_users()
            profile_count = self.migrate_user_profiles()
            todo_count = self.migrate_todos()
            achievement_count = self.migrate_achievements()
            plan_count = self.migrate_plans()
            
            print("\n=== 迁移完成 ===")
            print(f"用户: {user_count}")
            print(f"用户档案: {profile_count}")
            print(f"待办事项: {todo_count}")
            print(f"成就: {achievement_count}")
            print(f"计划: {plan_count}")
            
        except Exception as e:
            print(f"迁移过程中出错: {e}")
            raise
        finally:
            self.close_flask_db()


def main():
    """主函数"""
    # Flask数据库路径
    flask_db_path = "../backend/instance/dev.db"
    
    if not os.path.exists(flask_db_path):
        print(f"Flask数据库文件不存在: {flask_db_path}")
        print("请确保Flask应用已经运行并创建了数据库")
        return
    
    # 创建迁移器并运行迁移
    migrator = DataMigrator(flask_db_path)
    migrator.run_migration()


if __name__ == "__main__":
    main()
