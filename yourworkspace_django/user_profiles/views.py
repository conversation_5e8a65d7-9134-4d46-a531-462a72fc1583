"""
User Profile views for Django REST Framework
Migrated from Flask anchor_bp.py
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from .models import UserProfile
from .serializers import UserProfileSerializer, UserProfileUpdateSerializer


@api_view(['GET'])
@permission_classes([AllowAny])
def ping_profile(request):
    """Simple test route - migrated from Flask ping endpoint"""
    return Response({"message": "Profile API is alive!"}, status=status.HTTP_200_OK)


@api_view(['GET', 'PUT'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """
    获取或更新用户档案
    GET/PUT /api/v1/anchor/profile/
    Migrated from Flask anchor_bp profile endpoints
    """
    # 获取或创建用户档案
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'GET':
        # 获取用户档案
        serializer = UserProfileSerializer(profile)
        return Response(serializer.data, status=status.HTTP_200_OK)

    elif request.method == 'PUT':
        # 更新用户档案
        serializer = UserProfileUpdateSerializer(profile, data=request.data, partial=True)

        if serializer.is_valid():
            updated_profile = serializer.save()
            response_serializer = UserProfileSerializer(updated_profile)
            return Response(response_serializer.data, status=status.HTTP_200_OK)

        return Response({
            'error': 'Profile update failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
