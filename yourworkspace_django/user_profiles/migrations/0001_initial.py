# Generated by Django 5.2.4 on 2025-07-16 03:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='profile', serialize=False, to=settings.AUTH_USER_MODEL)),
                ('professional_title', models.CharField(blank=True, max_length=255, null=True)),
                ('one_liner_bio', models.TextField(blank=True, null=True)),
                ('skill', models.TextField(blank=True, help_text='User skills and expertise', null=True)),
                ('summary', models.TextField(blank=True, help_text='User summary/bio', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
                'db_table': 'user_profiles',
            },
        ),
    ]
