from django.contrib import admin
from .models import UserProfile


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """用户档案管理界面"""

    # 列表显示字段
    list_display = ['user', 'professional_title', 'one_liner_bio', 'created_at', 'updated_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['user__username', 'user__email', 'professional_title', 'one_liner_bio']
    ordering = ['-created_at']

    # 详细页面字段组织
    fieldsets = (
        ('用户信息', {
            'fields': ('user',)
        }),
        ('档案信息', {
            'fields': ('professional_title', 'one_liner_bio', 'skill', 'summary')
        }),
        ('时间戳', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    # 只读字段
    readonly_fields = ['created_at', 'updated_at']

    # 原始ID字段
    raw_id_fields = ['user']
