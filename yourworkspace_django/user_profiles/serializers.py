"""
User Profile serializers for Django REST Framework
"""

from rest_framework import serializers
from .models import UserProfile


class UserProfileSerializer(serializers.ModelSerializer):
    """用户档案序列化器"""
    
    class Meta:
        model = UserProfile
        fields = [
            'user', 'professional_title', 'one_liner_bio', 
            'skill', 'summary', 'created_at', 'updated_at'
        ]
        read_only_fields = ['user', 'created_at', 'updated_at']
    
    def validate_professional_title(self, value):
        """验证职业标题长度"""
        if value and len(value) > 255:
            raise serializers.ValidationError("职业标题不能超过255个字符")
        return value


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """用户档案更新序列化器"""
    
    class Meta:
        model = UserProfile
        fields = ['professional_title', 'one_liner_bio', 'skill', 'summary']
    
    def validate_professional_title(self, value):
        """验证职业标题长度"""
        if value and len(value) > 255:
            raise serializers.ValidationError("职业标题不能超过255个字符")
        return value
