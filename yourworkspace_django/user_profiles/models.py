from django.db import models
from django.conf import settings
from typing import Dict, Any


class UserProfile(models.Model):
    """
    UserProfile model for storing additional user profile information.
    One-to-one relationship with User model.
    Migrated from Flask SQLAlchemy UserProfile model.
    """
    # One-to-one relationship with User
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='profile'
    )

    # Profile fields
    professional_title = models.CharField(max_length=255, blank=True, null=True)
    one_liner_bio = models.TextField(blank=True, null=True)
    skill = models.TextField(blank=True, null=True, help_text="User skills and expertise")
    summary = models.TextField(blank=True, null=True, help_text="User summary/bio")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_profiles'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'

    def __str__(self):
        return f'Profile for {self.user.username}'

    def to_dict(self) -> Dict[str, Any]:
        """Convert UserProfile instance to dictionary for API responses."""
        return {
            'user_id': self.user.id,
            'professional_title': self.professional_title,
            'one_liner_bio': self.one_liner_bio,
            'skill': self.skill,
            'summary': self.summary,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
