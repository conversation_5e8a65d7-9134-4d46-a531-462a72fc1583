from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from typing import Dict, Any


class User(AbstractUser):
    """
    Custom User model extending Django's AbstractUser.
    Migrated from Flask SQLAlchemy User model.
    """
    # AbstractUser already provides: username, email, password, first_name, last_name,
    # is_active, is_staff, is_superuser, date_joined, last_login

    # Override email to make it unique and required
    email = models.EmailField(unique=True, blank=False)

    # Add custom timestamp fields to match Flask model
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Use email as the username field for authentication
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        db_table = 'users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.username} ({self.email})'

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert User instance to dictionary for API responses.
        Excludes sensitive information like password.
        """
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'date_joined': self.date_joined.isoformat() if self.date_joined else None,
        }

    def to_dict_with_profile(self) -> Dict[str, Any]:
        """
        Convert User and its profile to dictionary.
        Includes profile information if it exists.
        """
        user_data = self.to_dict()
        try:
            if hasattr(self, 'profile') and self.profile:
                user_data['profile'] = self.profile.to_dict()
            else:
                user_data['profile'] = None
        except:
            user_data['profile'] = None
        return user_data

    @staticmethod
    def format_datetime(dt):
        """Format datetime for API responses"""
        if dt is None:
            return None
        return dt.isoformat() + 'Z' if dt.tzinfo else dt.isoformat()

    @staticmethod
    def format_date(date):
        """Format date for API responses"""
        if date is None:
            return None
        return date.isoformat()
