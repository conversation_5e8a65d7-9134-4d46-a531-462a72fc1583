"""
Authentication views for Django REST Framework
Migrated from Flask auth_bp.py
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.hashers import check_password

from .models import User
from .serializers import (
    UserRegistrationSerializer,
    UserLoginSerializer,
    UserSerializer,
    ChangePasswordSerializer
)


@api_view(['GET'])
@permission_classes([AllowAny])
def ping_auth(request):
    """Simple test route - migrated from Flask ping endpoint"""
    return Response({"message": "Auth API is alive!"}, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    """
    User registration endpoint
    Migrated from Flask register endpoint
    """
    serializer = UserRegistrationSerializer(data=request.data)

    if serializer.is_valid():
        user = serializer.save()

        # Create authentication token
        token, created = Token.objects.get_or_create(user=user)

        # Return user data with token
        user_serializer = UserSerializer(user)
        return Response({
            'message': 'User registered successfully',
            'user': user_serializer.data,
            'access_token': token.key
        }, status=status.HTTP_201_CREATED)

    return Response({
        'error': 'Registration failed',
        'details': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def user_login(request):
    """
    User login endpoint
    Migrated from Flask login endpoint
    """
    serializer = UserLoginSerializer(data=request.data)

    if serializer.is_valid():
        user = serializer.validated_data['user']

        # Get or create authentication token
        token, created = Token.objects.get_or_create(user=user)

        # Return user data with token
        user_serializer = UserSerializer(user)
        return Response({
            'message': 'Login successful',
            'user': user_serializer.data,
            'access_token': token.key,
            'refresh_token': token.key  # For compatibility with Flask JWT structure
        }, status=status.HTTP_200_OK)

    return Response({
        'error': 'Login failed',
        'details': serializer.errors
    }, status=status.HTTP_401_UNAUTHORIZED)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def user_logout(request):
    """
    User logout endpoint
    Migrated from Flask logout endpoint
    """
    try:
        # Delete the user's token to logout
        token = Token.objects.get(user=request.user)
        token.delete()

        return Response({
            'message': 'Logout successful'
        }, status=status.HTTP_200_OK)

    except Token.DoesNotExist:
        return Response({
            'message': 'Already logged out'
        }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_current_user(request):
    """
    Get current user information
    Migrated from Flask me endpoint
    """
    serializer = UserSerializer(request.user)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def change_password(request):
    """
    Change user password
    Migrated from Flask change_password endpoint
    """
    serializer = ChangePasswordSerializer(data=request.data, context={'request': request})

    if serializer.is_valid():
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()

        # Delete old token and create new one for security
        try:
            old_token = Token.objects.get(user=user)
            old_token.delete()
        except Token.DoesNotExist:
            pass

        new_token = Token.objects.create(user=user)

        return Response({
            'message': 'Password changed successfully',
            'access_token': new_token.key
        }, status=status.HTTP_200_OK)

    return Response({
        'error': 'Password change failed',
        'details': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def refresh_token(request):
    """
    Refresh authentication token
    Migrated from Flask refresh endpoint
    """
    try:
        # Get existing token
        token = Token.objects.get(user=request.user)

        # For simplicity, return the same token
        # In production, you might want to implement token rotation
        user_serializer = UserSerializer(request.user)

        return Response({
            'message': 'Token refreshed',
            'user': user_serializer.data,
            'access_token': token.key,
            'refresh_token': token.key
        }, status=status.HTTP_200_OK)

    except Token.DoesNotExist:
        # Create new token if none exists
        token = Token.objects.create(user=request.user)
        user_serializer = UserSerializer(request.user)

        return Response({
            'message': 'New token created',
            'user': user_serializer.data,
            'access_token': token.key,
            'refresh_token': token.key
        }, status=status.HTTP_200_OK)
