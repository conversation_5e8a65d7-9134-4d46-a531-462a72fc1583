from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """自定义用户管理界面"""

    # 列表显示字段
    list_display = ['username', 'email', 'first_name', 'last_name', 'is_active', 'is_staff', 'date_joined', 'created_at']
    list_filter = ['is_active', 'is_staff', 'is_superuser', 'date_joined', 'created_at']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering = ['-date_joined']

    # 详细页面字段组织
    fieldsets = BaseUserAdmin.fieldsets + (
        ('时间戳', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    # 只读字段
    readonly_fields = ['date_joined', 'last_login', 'created_at', 'updated_at']

    # 添加用户页面字段
    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('个人信息', {
            'fields': ('first_name', 'last_name', 'email'),
        }),
    )
