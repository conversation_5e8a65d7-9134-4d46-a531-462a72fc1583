"""
Authentication URL configuration
Migrated from Flask auth_bp routes
"""

from django.urls import path
from . import views

app_name = 'authentication'

urlpatterns = [
    # Health check
    path('ping/', views.ping_auth, name='ping'),
    
    # Authentication endpoints
    path('register/', views.register, name='register'),
    path('login/', views.user_login, name='login'),
    path('logout/', views.user_logout, name='logout'),
    path('refresh/', views.refresh_token, name='refresh'),
    
    # User management
    path('me/', views.get_current_user, name='current_user'),
    path('change-password/', views.change_password, name='change_password'),
]
