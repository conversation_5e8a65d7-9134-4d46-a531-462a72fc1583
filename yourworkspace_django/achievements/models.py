from django.db import models
from django.conf import settings
from typing import Dict, Any, List


class Achievement(models.Model):
    """
    Achievement model for storing user's past accomplishments.
    Migrated from Flask SQLAlchemy Achievement model.
    """

    # Foreign key to User
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='achievements'
    )

    # Achievement fields
    title = models.TextField()
    description = models.TextField(blank=True, null=True)
    quantifiable_results = models.TextField(
        blank=True,
        null=True,
        help_text="Measurable outcomes and results"
    )

    # JSON field for storing skills list
    core_skills_json = models.JSONField(
        default=list,
        blank=True,
        help_text="List of core skills demonstrated in this achievement"
    )

    date_achieved = models.DateField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'achievements'
        verbose_name = 'Achievement'
        verbose_name_plural = 'Achievements'
        ordering = ['-date_achieved', '-created_at']
        indexes = [
            models.Index(fields=['user', 'date_achieved']),
        ]

    def __str__(self):
        return f'{self.title[:50]}...' if len(self.title) > 50 else self.title

    def to_dict(self) -> Dict[str, Any]:
        """Convert Achievement instance to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user.id,
            'title': self.title,
            'description': self.description,
            'quantifiable_results': self.quantifiable_results,
            'core_skills_json': self.core_skills_json if self.core_skills_json else [],
            'date_achieved': self.date_achieved.isoformat() if self.date_achieved else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
