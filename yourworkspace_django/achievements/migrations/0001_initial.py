# Generated by Django 5.2.4 on 2025-07-16 03:03

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Achievement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.TextField()),
                ('description', models.TextField(blank=True, null=True)),
                ('quantifiable_results', models.TextField(blank=True, help_text='Measurable outcomes and results', null=True)),
                ('core_skills_json', models.J<PERSON><PERSON>ield(blank=True, default=list, help_text='List of core skills demonstrated in this achievement')),
                ('date_achieved', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Achievement',
                'verbose_name_plural': 'Achievements',
                'db_table': 'achievements',
                'ordering': ['-date_achieved', '-created_at'],
            },
        ),
    ]
