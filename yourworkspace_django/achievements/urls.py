"""
Achievement URL configuration
Migrated from Flask achievements_bp routes
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'achievements'

# Create a router and register our viewset
router = DefaultRouter()
router.register(r'', views.AchievementViewSet, basename='achievement')

urlpatterns = [
    # Health check
    path('ping/', views.ping_achievement, name='ping'),
    
    # Include router URLs
    path('', include(router.urls)),
]
