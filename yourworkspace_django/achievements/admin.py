from django.contrib import admin
from .models import Achievement


@admin.register(Achievement)
class AchievementAdmin(admin.ModelAdmin):
    """成就管理界面"""

    # 列表显示字段
    list_display = ['title', 'user', 'date_achieved', 'created_at']
    list_filter = ['date_achieved', 'created_at']
    search_fields = ['title', 'description', 'user__username', 'user__email', 'quantifiable_results']
    ordering = ['-date_achieved', '-created_at']
    date_hierarchy = 'date_achieved'

    # 详细页面字段组织
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'title', 'description')
        }),
        ('成就详情', {
            'fields': ('quantifiable_results', 'core_skills_json', 'date_achieved')
        }),
        ('时间戳', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    # 只读字段
    readonly_fields = ['created_at', 'updated_at']

    # 原始ID字段
    raw_id_fields = ['user']

    # 自定义显示
    def get_core_skills_display(self, obj):
        """显示核心技能"""
        if obj.core_skills_json:
            return ', '.join(obj.core_skills_json)
        return '-'
    get_core_skills_display.short_description = '核心技能'
