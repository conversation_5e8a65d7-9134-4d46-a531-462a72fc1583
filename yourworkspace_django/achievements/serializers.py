"""
Achievement serializers for Django REST Framework
"""

from rest_framework import serializers
from django.utils import timezone
from .models import Achievement


class AchievementSerializer(serializers.ModelSerializer):
    """成就序列化器"""
    
    class Meta:
        model = Achievement
        fields = [
            'id', 'title', 'description', 'quantifiable_results',
            'core_skills_json', 'date_achieved', 'created_at', 'updated_at', 'user'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'user']
    
    def validate_date_achieved(self, value):
        """验证成就日期"""
        if value and value > timezone.now().date():
            raise serializers.ValidationError("成就日期不能是未来的时间")
        return value
    
    def validate_core_skills_json(self, value):
        """验证技能列表"""
        if value is not None and not isinstance(value, list):
            raise serializers.ValidationError("技能必须是一个列表")
        return value
    
    def create(self, validated_data):
        """创建成就"""
        user = self.context['request'].user
        validated_data['user'] = user
        return super().create(validated_data)


class AchievementCreateSerializer(serializers.ModelSerializer):
    """创建成就的序列化器"""
    
    class Meta:
        model = Achievement
        fields = ['title', 'description', 'quantifiable_results', 'core_skills_json', 'date_achieved']
    
    def validate_date_achieved(self, value):
        """验证成就日期"""
        if value and value > timezone.now().date():
            raise serializers.ValidationError("成就日期不能是未来的时间")
        return value
    
    def validate_core_skills_json(self, value):
        """验证技能列表"""
        if value is not None and not isinstance(value, list):
            raise serializers.ValidationError("技能必须是一个列表")
        return value
    
    def create(self, validated_data):
        """创建成就"""
        user = self.context['request'].user
        validated_data['user'] = user
        return Achievement.objects.create(**validated_data)


class AchievementUpdateSerializer(serializers.ModelSerializer):
    """更新成就的序列化器"""
    
    class Meta:
        model = Achievement
        fields = ['title', 'description', 'quantifiable_results', 'core_skills_json', 'date_achieved']
    
    def validate_date_achieved(self, value):
        """验证成就日期"""
        if value and value > timezone.now().date():
            raise serializers.ValidationError("成就日期不能是未来的时间")
        return value
    
    def validate_core_skills_json(self, value):
        """验证技能列表"""
        if value is not None and not isinstance(value, list):
            raise serializers.ValidationError("技能必须是一个列表")
        return value
