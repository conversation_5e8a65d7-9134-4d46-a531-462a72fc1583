"""
Achievement views for Django REST Framework
Migrated from Flask achievements_bp.py
"""

from rest_framework import status, viewsets
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from .models import Achievement
from .serializers import (
    AchievementSerializer,
    AchievementCreateSerializer,
    AchievementUpdateSerializer
)


@api_view(['GET'])
@permission_classes([AllowAny])
def ping_achievement(request):
    """Simple test route - migrated from Flask ping endpoint"""
    return Response({"message": "Achievement API is alive!"}, status=status.HTTP_200_OK)


class AchievementViewSet(viewsets.ModelViewSet):
    """
    成就视图集
    Migrated from Flask achievements_bp endpoints
    """
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """获取当前用户的成就，按日期和创建时间排序"""
        return Achievement.objects.filter(user=self.request.user).order_by(
            '-date_achieved', '-created_at'
        )

    def get_serializer_class(self):
        """根据操作选择序列化器"""
        if self.action == 'create':
            return AchievementCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return AchievementUpdateSerializer
        return AchievementSerializer

    def list(self, request):
        """
        获取所有成就
        GET /api/v1/achievements/
        """
        queryset = self.get_queryset()
        serializer = AchievementSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def create(self, request):
        """
        创建新的成就
        POST /api/v1/achievements/
        """
        serializer = AchievementCreateSerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            achievement = serializer.save()
            response_serializer = AchievementSerializer(achievement)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response({
            'error': 'Achievement creation failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, pk=None):
        """
        获取单个成就
        GET /api/v1/achievements/{id}/
        """
        achievement = get_object_or_404(Achievement, pk=pk, user=request.user)
        serializer = AchievementSerializer(achievement)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def update(self, request, pk=None):
        """
        完整更新成就
        PUT /api/v1/achievements/{id}/
        """
        achievement = get_object_or_404(Achievement, pk=pk, user=request.user)
        serializer = AchievementUpdateSerializer(achievement, data=request.data, context={'request': request})

        if serializer.is_valid():
            updated_achievement = serializer.save()
            response_serializer = AchievementSerializer(updated_achievement)
            return Response(response_serializer.data, status=status.HTTP_200_OK)

        return Response({
            'error': 'Achievement update failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def partial_update(self, request, pk=None):
        """
        部分更新成就
        PATCH /api/v1/achievements/{id}/
        """
        achievement = get_object_or_404(Achievement, pk=pk, user=request.user)
        serializer = AchievementUpdateSerializer(achievement, data=request.data, partial=True, context={'request': request})

        if serializer.is_valid():
            updated_achievement = serializer.save()
            response_serializer = AchievementSerializer(updated_achievement)
            return Response(response_serializer.data, status=status.HTTP_200_OK)

        return Response({
            'error': 'Achievement update failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk=None):
        """
        删除成就
        DELETE /api/v1/achievements/{id}/
        """
        achievement = get_object_or_404(Achievement, pk=pk, user=request.user)
        achievement.delete()
        return Response({
            'message': 'Achievement deleted successfully'
        }, status=status.HTTP_204_NO_CONTENT)
