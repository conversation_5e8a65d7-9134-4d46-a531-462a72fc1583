# YourWorkspace Django API

一个现代化的任务管理系统，从Flask成功迁移到Django，提供强大的REST API和管理界面。

## 🚀 功能特性

- **用户认证**: 基于Token的认证系统
- **任务管理**: 创建、更新、删除和管理待办事项
- **成就跟踪**: 记录和管理个人成就
- **计划管理**: 制定和跟踪未来计划
- **用户档案**: 个人信息和技能管理
- **管理后台**: Django Admin界面
- **API文档**: RESTful API设计

## 🛠️ 技术栈

- **后端**: Django 5.2.4 + Django REST Framework
- **数据库**: PostgreSQL (生产) / SQLite (开发)
- **缓存**: Redis
- **部署**: Docker + Docker Compose + Nginx
- **监控**: Sentry (可选)

## 📦 快速开始

### 开发环境

1. **克隆项目**
```bash
git clone <repository-url>
cd yourworkspace_django
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，设置必要的环境变量
```

5. **运行迁移**
```bash
python manage.py migrate
```

6. **创建超级用户**
```bash
python manage.py createsuperuser
```

7. **启动开发服务器**
```bash
python manage.py runserver
```

### 生产环境部署

1. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，设置生产环境配置
```

2. **运行部署脚本**
```bash
./deploy.sh
```

## 📚 API 文档

### 认证端点

- `POST /api/v1/auth/register/` - 用户注册
- `POST /api/v1/auth/login/` - 用户登录
- `POST /api/v1/auth/logout/` - 用户登出
- `GET /api/v1/auth/me/` - 获取当前用户信息
- `POST /api/v1/auth/refresh/` - 刷新Token

### 待办事项端点

- `GET /api/v1/todo/todos/` - 获取待办事项列表
- `POST /api/v1/todo/todos/` - 创建待办事项
- `GET /api/v1/todo/todos/{id}/` - 获取单个待办事项
- `PUT /api/v1/todo/todos/{id}/` - 更新待办事项
- `DELETE /api/v1/todo/todos/{id}/` - 删除待办事项
- `POST /api/v1/todo/todos/{id}/mark_completed/` - 标记为完成

### 用户档案端点

- `GET /api/v1/anchor/profile/` - 获取用户档案
- `PUT /api/v1/anchor/profile/` - 更新用户档案

### 成就端点

- `GET /api/v1/achievements/` - 获取成就列表
- `POST /api/v1/achievements/` - 创建成就
- `GET /api/v1/achievements/{id}/` - 获取单个成就
- `PUT /api/v1/achievements/{id}/` - 更新成就
- `DELETE /api/v1/achievements/{id}/` - 删除成就

### 计划端点

- `GET /api/v1/plans/` - 获取计划列表
- `POST /api/v1/plans/` - 创建计划
- `GET /api/v1/plans/{id}/` - 获取单个计划
- `PUT /api/v1/plans/{id}/` - 更新计划
- `DELETE /api/v1/plans/{id}/` - 删除计划

## 🔧 开发指南

### 运行测试

```bash
python manage.py test
```

### 代码格式化

```bash
black .
isort .
```

### 数据库迁移

```bash
python manage.py makemigrations
python manage.py migrate
```

### 收集静态文件

```bash
python manage.py collectstatic
```

## 🐳 Docker 部署

### 开发环境

```bash
docker-compose -f docker-compose.dev.yml up
```

### 生产环境

```bash
docker-compose up -d
```

## 📊 监控和日志

- **应用日志**: `logs/django.log`
- **Nginx日志**: Docker容器内的 `/var/log/nginx/`
- **数据库日志**: PostgreSQL容器日志
- **错误跟踪**: Sentry (如果配置)

## 🔒 安全配置

- HTTPS强制重定向
- HSTS头部
- CSRF保护
- XSS保护
- 内容类型嗅探保护
- 点击劫持保护
- API速率限制

## 🚀 性能优化

- Redis缓存
- 静态文件压缩
- Gzip压缩
- 数据库索引优化
- 查询优化

## 📝 从Flask迁移

本项目是从Flask应用成功迁移而来，保持了API兼容性：

- ✅ 所有API端点保持相同的URL结构
- ✅ 请求/响应格式保持一致
- ✅ 数据完整性得到保证
- ✅ 用户认证系统平滑过渡

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请创建 Issue 或联系维护者。
