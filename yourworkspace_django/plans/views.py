"""
Plan views for Django REST Framework
Migrated from Flask plans_bp.py
"""

from rest_framework import status, viewsets
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from .models import FuturePlan
from .serializers import (
    FuturePlanSerializer,
    FuturePlanCreateSerializer,
    FuturePlanUpdateSerializer
)


@api_view(['GET'])
@permission_classes([AllowAny])
def ping_plan(request):
    """Simple test route - migrated from Flask ping endpoint"""
    return Response({"message": "Plan API is alive!"}, status=status.HTTP_200_OK)


class FuturePlanViewSet(viewsets.ModelViewSet):
    """
    计划视图集
    Migrated from Flask plans_bp endpoints
    """
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """获取当前用户的计划，按目标日期和创建时间排序"""
        return FuturePlan.objects.filter(user=self.request.user).order_by(
            'target_date', '-created_at'
        )

    def get_serializer_class(self):
        """根据操作选择序列化器"""
        if self.action == 'create':
            return FuturePlanCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return FuturePlanUpdateSerializer
        return FuturePlanSerializer

    def list(self, request):
        """
        获取所有计划
        GET /api/v1/plans/
        """
        queryset = self.get_queryset()
        serializer = FuturePlanSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def create(self, request):
        """
        创建新的计划
        POST /api/v1/plans/
        """
        serializer = FuturePlanCreateSerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            plan = serializer.save()
            response_serializer = FuturePlanSerializer(plan)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response({
            'error': 'Plan creation failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, pk=None):
        """
        获取单个计划
        GET /api/v1/plans/{id}/
        """
        plan = get_object_or_404(FuturePlan, pk=pk, user=request.user)
        serializer = FuturePlanSerializer(plan)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def update(self, request, pk=None):
        """
        完整更新计划
        PUT /api/v1/plans/{id}/
        """
        plan = get_object_or_404(FuturePlan, pk=pk, user=request.user)
        serializer = FuturePlanUpdateSerializer(plan, data=request.data, context={'request': request})

        if serializer.is_valid():
            updated_plan = serializer.save()
            response_serializer = FuturePlanSerializer(updated_plan)
            return Response(response_serializer.data, status=status.HTTP_200_OK)

        return Response({
            'error': 'Plan update failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def partial_update(self, request, pk=None):
        """
        部分更新计划
        PATCH /api/v1/plans/{id}/
        """
        plan = get_object_or_404(FuturePlan, pk=pk, user=request.user)
        serializer = FuturePlanUpdateSerializer(plan, data=request.data, partial=True, context={'request': request})

        if serializer.is_valid():
            updated_plan = serializer.save()
            response_serializer = FuturePlanSerializer(updated_plan)
            return Response(response_serializer.data, status=status.HTTP_200_OK)

        return Response({
            'error': 'Plan update failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk=None):
        """
        删除计划
        DELETE /api/v1/plans/{id}/
        """
        plan = get_object_or_404(FuturePlan, pk=pk, user=request.user)
        plan.delete()
        return Response({
            'message': 'Plan deleted successfully'
        }, status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['post'])
    def mark_achieved(self, request, pk=None):
        """
        标记计划为已完成
        POST /api/v1/plans/{id}/mark_achieved/
        """
        plan = get_object_or_404(FuturePlan, pk=pk, user=request.user)
        plan.status = 'achieved'
        plan.save()

        serializer = FuturePlanSerializer(plan)
        return Response({
            'message': 'Plan marked as achieved',
            'plan': serializer.data
        }, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def active_plans(self, request):
        """
        获取活跃的计划
        GET /api/v1/plans/active_plans/
        """
        active_plans = FuturePlan.objects.filter(
            user=request.user,
            status='active'
        ).order_by('target_date', '-created_at')

        serializer = FuturePlanSerializer(active_plans, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
