from django.db import models
from django.conf import settings
from typing import Dict, Any


class FuturePlan(models.Model):
    """
    FuturePlan model for storing user's future goals, plans, or vision items.
    Migrated from Flask SQLAlchemy FuturePlan model.
    """

    GOAL_TYPE_CHOICES = [
        ('short_term', 'Short Term Goal'),
        ('long_term', 'Long Term Vision'),
        ('skill_development', 'Skill Development'),
        ('career', 'Career Goal'),
        ('personal', 'Personal Goal'),
        ('project', 'Project Goal'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('achieved', 'Achieved'),
        ('deferred', 'Deferred'),
        ('abandoned', 'Abandoned'),
    ]

    # Foreign key to User
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='future_plans'
    )

    # Plan fields
    goal_type = models.CharField(
        max_length=50,
        choices=GOAL_TYPE_CHOICES,
        blank=True,
        null=True
    )
    title = models.TextField()
    description = models.TextField()
    target_date = models.DateField(blank=True, null=True)

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'future_plans'
        verbose_name = 'Future Plan'
        verbose_name_plural = 'Future Plans'
        ordering = ['target_date', '-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['target_date']),
            models.Index(fields=['goal_type']),
        ]

    def __str__(self):
        return f'{self.title[:50]}...' if len(self.title) > 50 else self.title

    def to_dict(self) -> Dict[str, Any]:
        """Convert FuturePlan instance to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user.id,
            'goal_type': self.goal_type,
            'title': self.title,
            'description': self.description,
            'target_date': self.target_date.isoformat() if self.target_date else None,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
