"""
Plan URL configuration
Migrated from Flask plans_bp routes
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'plans'

# Create a router and register our viewset
router = DefaultRouter()
router.register(r'', views.FuturePlanViewSet, basename='futureplan')

urlpatterns = [
    # Health check
    path('ping/', views.ping_plan, name='ping'),
    
    # Include router URLs
    path('', include(router.urls)),
]
