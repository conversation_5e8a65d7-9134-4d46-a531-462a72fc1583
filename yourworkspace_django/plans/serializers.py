"""
Plan serializers for Django REST Framework
"""

from rest_framework import serializers
from django.utils import timezone
from .models import FuturePlan


class FuturePlanSerializer(serializers.ModelSerializer):
    """计划序列化器"""
    
    class Meta:
        model = FuturePlan
        fields = [
            'id', 'goal_type', 'title', 'description', 'target_date',
            'status', 'created_at', 'updated_at', 'user'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'user']
    
    def validate_target_date(self, value):
        """验证目标日期"""
        if value and value < timezone.now().date():
            raise serializers.ValidationError("目标日期不能是过去的时间")
        return value
    
    def validate_goal_type(self, value):
        """验证目标类型"""
        allowed_types = ['short_term', 'long_term', 'skill_development', 'career', 'personal', 'project']
        if value and value not in allowed_types:
            raise serializers.ValidationError(f"目标类型必须是以下之一: {', '.join(allowed_types)}")
        return value
    
    def validate_status(self, value):
        """验证状态"""
        allowed_statuses = ['active', 'achieved', 'deferred', 'abandoned']
        if value not in allowed_statuses:
            raise serializers.ValidationError(f"状态必须是以下之一: {', '.join(allowed_statuses)}")
        return value
    
    def create(self, validated_data):
        """创建计划"""
        user = self.context['request'].user
        validated_data['user'] = user
        return super().create(validated_data)


class FuturePlanCreateSerializer(serializers.ModelSerializer):
    """创建计划的序列化器"""
    
    class Meta:
        model = FuturePlan
        fields = ['goal_type', 'title', 'description', 'target_date']
    
    def validate_target_date(self, value):
        """验证目标日期"""
        if value and value < timezone.now().date():
            raise serializers.ValidationError("目标日期不能是过去的时间")
        return value
    
    def validate_goal_type(self, value):
        """验证目标类型"""
        allowed_types = ['short_term', 'long_term', 'skill_development', 'career', 'personal', 'project']
        if value and value not in allowed_types:
            raise serializers.ValidationError(f"目标类型必须是以下之一: {', '.join(allowed_types)}")
        return value
    
    def create(self, validated_data):
        """创建计划"""
        user = self.context['request'].user
        validated_data['user'] = user
        validated_data['status'] = 'active'  # 默认状态
        return FuturePlan.objects.create(**validated_data)


class FuturePlanUpdateSerializer(serializers.ModelSerializer):
    """更新计划的序列化器"""
    
    class Meta:
        model = FuturePlan
        fields = ['goal_type', 'title', 'description', 'target_date', 'status']
    
    def validate_target_date(self, value):
        """验证目标日期"""
        if value and value < timezone.now().date():
            raise serializers.ValidationError("目标日期不能是过去的时间")
        return value
    
    def validate_goal_type(self, value):
        """验证目标类型"""
        allowed_types = ['short_term', 'long_term', 'skill_development', 'career', 'personal', 'project']
        if value and value not in allowed_types:
            raise serializers.ValidationError(f"目标类型必须是以下之一: {', '.join(allowed_types)}")
        return value
    
    def validate_status(self, value):
        """验证状态"""
        allowed_statuses = ['active', 'achieved', 'deferred', 'abandoned']
        if value not in allowed_statuses:
            raise serializers.ValidationError(f"状态必须是以下之一: {', '.join(allowed_statuses)}")
        return value
