from django.contrib import admin
from .models import FuturePlan


@admin.register(FuturePlan)
class FuturePlanAdmin(admin.ModelAdmin):
    """计划管理界面"""

    # 列表显示字段
    list_display = ['title', 'user', 'goal_type', 'status', 'target_date', 'created_at']
    list_filter = ['goal_type', 'status', 'target_date', 'created_at']
    search_fields = ['title', 'description', 'user__username', 'user__email']
    list_editable = ['status']
    ordering = ['target_date', '-created_at']
    date_hierarchy = 'target_date'

    # 详细页面字段组织
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'goal_type', 'title', 'description')
        }),
        ('计划详情', {
            'fields': ('target_date', 'status')
        }),
        ('时间戳', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    # 只读字段
    readonly_fields = ['created_at', 'updated_at']

    # 原始ID字段
    raw_id_fields = ['user']

    # 自定义操作
    actions = ['mark_as_achieved', 'mark_as_active', 'mark_as_deferred']

    def mark_as_achieved(self, request, queryset):
        """批量标记为已完成"""
        updated = queryset.update(status='achieved')
        self.message_user(request, f'{updated} 个计划已标记为已完成')
    mark_as_achieved.short_description = '标记为已完成'

    def mark_as_active(self, request, queryset):
        """批量标记为活跃"""
        updated = queryset.update(status='active')
        self.message_user(request, f'{updated} 个计划已标记为活跃')
    mark_as_active.short_description = '标记为活跃'

    def mark_as_deferred(self, request, queryset):
        """批量标记为延期"""
        updated = queryset.update(status='deferred')
        self.message_user(request, f'{updated} 个计划已标记为延期')
    mark_as_deferred.short_description = '标记为延期'
