# Generated by Django 5.2.4 on 2025-07-16 03:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FuturePlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('goal_type', models.CharField(blank=True, choices=[('short_term', 'Short Term Goal'), ('long_term', 'Long Term Vision'), ('skill_development', 'Skill Development'), ('career', 'Career Goal'), ('personal', 'Personal Goal'), ('project', 'Project Goal')], max_length=50, null=True)),
                ('title', models.TextField()),
                ('description', models.TextField()),
                ('target_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('achieved', 'Achieved'), ('deferred', 'Deferred'), ('abandoned', 'Abandoned')], default='active', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='future_plans', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Future Plan',
                'verbose_name_plural': 'Future Plans',
                'db_table': 'future_plans',
                'ordering': ['target_date', '-created_at'],
                'indexes': [models.Index(fields=['user', 'status'], name='future_plan_user_id_f27e0c_idx'), models.Index(fields=['target_date'], name='future_plan_target__e699f9_idx'), models.Index(fields=['goal_type'], name='future_plan_goal_ty_035865_idx')],
            },
        ),
    ]
