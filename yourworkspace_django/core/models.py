from django.db import models
from typing import Dict, Any


class BaseModel(models.Model):
    """
    Abstract base model with common functionality.
    Provides common timestamp fields and utility methods.
    """
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

    @staticmethod
    def format_datetime(dt):
        """Format datetime for API responses"""
        if dt is None:
            return None
        return dt.isoformat() + 'Z' if dt.tzinfo else dt.isoformat()

    @staticmethod
    def format_date(date):
        """Format date for API responses"""
        if date is None:
            return None
        return date.isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """
        Base implementation of to_dict - should be overridden in subclasses.
        """
        raise NotImplementedError("Subclasses must implement to_dict()")
