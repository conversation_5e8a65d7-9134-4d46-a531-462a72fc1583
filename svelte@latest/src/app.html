<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%sveltekit.assets%/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="description"
      content="Your personal workspace for managing tasks, achievements, and future plans"
    />
    <meta name="theme-color" content="#4f46e5" />

    <!-- Preload critical assets -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Prevent flash of unstyled content -->
    <script>
      // Check for dark mode preference
      (function () {
        const prefersDark =
          window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const storedTheme = localStorage.getItem('app_theme_settings');
        let darkMode = prefersDark;

        // Check stored theme settings
        if (storedTheme) {
          try {
            const settings = JSON.parse(storedTheme);
            darkMode = settings.darkMode;
          } catch (e) {
            console.error('Failed to parse theme settings');
          }
        }

        // Apply dark mode immediately to prevent flash
        if (darkMode) {
          document.documentElement.classList.add('dark');
        }

        // Apply custom background if set
        if (storedTheme) {
          try {
            const settings = JSON.parse(storedTheme);
            if (settings.customBackground) {
              document.documentElement.style.setProperty(
                '--custom-background',
                `url(${settings.customBackground})`
              );
              document.documentElement.classList.add('has-custom-bg');
              // 确保背景在页面加载时立即可见
              document.addEventListener('DOMContentLoaded', function () {
                document.body.classList.add('has-background');
                document.body.style.backgroundColor = 'transparent';
                document.documentElement.style.backgroundColor = 'transparent';

                // 如果是暗色模式，添加特殊类
                if (darkMode) {
                  document.body.classList.add('dark-with-background');
                }
              });
            } else {
              document.documentElement.style.setProperty('--custom-background', 'none');
              document.documentElement.classList.remove('has-custom-bg');
            }
          } catch (e) {
            console.error('Failed to apply custom background');
          }
        }
      })();
    </script>

    %sveltekit.head%
  </head>
  <body data-sveltekit-preload-data="hover">
    <div style="display: contents">%sveltekit.body%</div>
    <script src="%sveltekit.assets%/js/anchor-button.js"></script>
  </body>
</html>
