/**
 * Sentry configuration for error tracking and performance monitoring
 */

import * as Sentry from '@sentry/svelte';
import { browser } from '$app/environment';

// Initialize Sentry only in browser environment
export function initSentry() {
  if (!browser) return;

  const dsn = import.meta.env.VITE_SENTRY_DSN;
  const environment = import.meta.env.VITE_SENTRY_ENVIRONMENT || 'development';

  if (!dsn) {
    console.warn('Sentry DSN not configured. Error tracking disabled.');
    return;
  }

  Sentry.init({
    dsn,
    environment,
    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration({
        maskAllText: false,
        blockAllMedia: false,
      }),
    ],

    // Performance Monitoring
    tracesSampleRate: environment === 'production' ? 0.1 : 1.0,

    // Session Replay
    replaysSessionSampleRate: environment === 'production' ? 0.1 : 1.0,
    replaysOnErrorSampleRate: 1.0,

    // Release tracking
    release: import.meta.env.VITE_APP_VERSION || 'unknown',

    // Additional configuration
    beforeSend(event, hint) {
      // Filter out development errors in production
      if (environment === 'production') {
        // Don't send console errors in production
        if (event.exception?.values?.[0]?.type === 'ConsoleError') {
          return null;
        }
      }

      // Log errors to console in development
      if (environment === 'development') {
        console.error('Sentry captured error:', event, hint);
      }

      return event;
    },

    // Set user context
    initialScope: {
      tags: {
        component: 'svelte-frontend',
      },
    },
  });

  console.log(`Sentry initialized for ${environment} environment`);
}

// Helper functions for manual error reporting
export function captureError(error: Error, context?: Record<string, any>) {
  if (!browser) return;

  Sentry.withScope(scope => {
    if (context) {
      Object.entries(context).forEach(([key, value]) => {
        scope.setContext(key, value);
      });
    }
    Sentry.captureException(error);
  });
}

export function captureMessage(
  message: string,
  level: Sentry.SeverityLevel = 'info',
  context?: Record<string, any>
) {
  if (!browser) return;

  Sentry.withScope(scope => {
    if (context) {
      Object.entries(context).forEach(([key, value]) => {
        scope.setContext(key, value);
      });
    }
    Sentry.captureMessage(message, level);
  });
}

export function setUserContext(user: { id: string | number; email?: string; username?: string }) {
  if (!browser) return;

  Sentry.setUser({
    id: String(user.id),
    email: user.email,
    username: user.username,
  });
}

export function clearUserContext() {
  if (!browser) return;

  Sentry.setUser(null);
}

export function addBreadcrumb(message: string, category: string, data?: Record<string, any>) {
  if (!browser) return;

  Sentry.addBreadcrumb({
    message,
    category,
    data,
    level: 'info',
  });
}
