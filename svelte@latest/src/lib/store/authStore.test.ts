import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import { authStore, isAuthenticated } from './authStore';
import { mockLocalStorage, createMockUser } from '../../test/utils';

// Mock browser environment
vi.mock('$app/environment', () => ({
  browser: true,
}));

describe('authStore', () => {
  let mockStorage: ReturnType<typeof mockLocalStorage>;

  beforeEach(() => {
    // Reset store state
    authStore.logout();
    
    // Mock localStorage
    mockStorage = mockLocalStorage();
    Object.defineProperty(window, 'localStorage', {
      value: mockStorage,
      writable: true,
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = get(authStore);

      expect(get(isAuthenticated)).toBe(false);
      expect(state.accessToken).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.user).toBeNull();
    });
  });

  describe('setTokens', () => {
    it('should set tokens and update authentication state', () => {
      const accessToken = 'test-access-token';
      const refreshToken = 'test-refresh-token';

      authStore.setTokens({ accessToken, refreshToken });
      const state = get(authStore);

      expect(get(isAuthenticated)).toBe(true);
      expect(state.accessToken).toBe(accessToken);
      expect(state.refreshToken).toBe(refreshToken);
      expect(mockStorage.setItem).toHaveBeenCalledWith('app_auth_state', expect.stringContaining(accessToken));
    });

    it('should handle missing refresh token', () => {
      const accessToken = 'test-access-token';

      authStore.setTokens({ accessToken });
      const state = get(authStore);

      expect(get(isAuthenticated)).toBe(true);
      expect(state.accessToken).toBe(accessToken);
      expect(state.refreshToken).toBeNull();
    });
  });

  describe('setUserProfile', () => {
    it('should set user profile', () => {
      const mockUser = createMockUser();

      authStore.setUserProfile(mockUser);
      const state = get(authStore);

      expect(state.user).toEqual(mockUser);
    });

    it('should handle null user profile', () => {
      authStore.setUserProfile(null);
      const state = get(authStore);

      expect(state.user).toBeNull();
    });
  });



  describe('logout', () => {
    it('should clear all authentication data', () => {
      // Set up authenticated state
      const mockUser = createMockUser();
      authStore.setTokens({ accessToken: 'access-token', refreshToken: 'refresh-token' });
      authStore.setUserProfile(mockUser);

      // Logout
      authStore.logout();
      const state = get(authStore);

      expect(get(isAuthenticated)).toBe(false);
      expect(state.accessToken).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.user).toBeNull();
      expect(mockStorage.removeItem).toHaveBeenCalledWith('app_auth_state');
    });
  });

  describe('derived stores', () => {
    it('should provide correct isAuthenticated derived value', () => {
      expect(get(isAuthenticated)).toBe(false);

      authStore.setTokens({ accessToken: 'test-token' });
      expect(get(isAuthenticated)).toBe(true);

      authStore.logout();
      expect(get(isAuthenticated)).toBe(false);
    });
  });

  describe('localStorage integration', () => {
    it('should persist state to localStorage', () => {
      const accessToken = 'test-access-token';
      const refreshToken = 'test-refresh-token';

      authStore.setTokens({ accessToken, refreshToken });

      expect(mockStorage.setItem).toHaveBeenCalledWith('app_auth_state', expect.stringContaining(accessToken));
    });

    it('should remove state from localStorage on logout', () => {
      authStore.setTokens({ accessToken: 'access-token', refreshToken: 'refresh-token' });
      authStore.logout();

      expect(mockStorage.removeItem).toHaveBeenCalledWith('app_auth_state');
    });
  });
});
