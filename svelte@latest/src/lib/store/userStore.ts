/**
 * User Store - Manages user profile data and state
 */

import { writable } from 'svelte/store';

export interface UserProfile {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  date_joined: string;
  created_at: string;
  updated_at: string;
  profile?: {
    user_id: number;
    professional_title?: string;
    one_liner_bio?: string;
    skill?: string;
    summary?: string;
    created_at: string;
    updated_at: string;
  } | null;
}

interface UserState {
  user: UserProfile | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: UserState = {
  user: null,
  isLoading: false,
  error: null
};

// Create the store
function createUserStore() {
  const { subscribe, set, update } = writable<UserState>(initialState);

  return {
    subscribe,
    
    // Set user data
    setUser: (user: UserProfile) => {
      update(state => ({
        ...state,
        user,
        error: null,
        isLoading: false
      }));
    },
    
    // Update user profile
    updateUser: (updates: Partial<UserProfile>) => {
      update(state => ({
        ...state,
        user: state.user ? { ...state.user, ...updates } : null
      }));
    },
    
    // Update user profile data
    updateProfile: (profileUpdates: Partial<UserProfile['profile']>) => {
      update(state => ({
        ...state,
        user: state.user ? {
          ...state.user,
          profile: state.user.profile ? 
            { ...state.user.profile, ...profileUpdates } : 
            profileUpdates as UserProfile['profile']
        } : null
      }));
    },
    
    // Set loading state
    setLoading: (isLoading: boolean) => {
      update(state => ({
        ...state,
        isLoading
      }));
    },
    
    // Set error
    setError: (error: string | null) => {
      update(state => ({
        ...state,
        error,
        isLoading: false
      }));
    },
    
    // Clear user data
    clearUser: () => {
      set(initialState);
    },
    
    // Reset store to initial state
    reset: () => {
      set(initialState);
    }
  };
}

export const userStore = createUserStore();

// Derived stores for easy access
import { derived } from 'svelte/store';

export const currentUser = derived(userStore, $userStore => $userStore.user);
export const userProfile = derived(userStore, $userStore => $userStore.user?.profile);
export const isUserLoading = derived(userStore, $userStore => $userStore.isLoading);
export const userError = derived(userStore, $userStore => $userStore.error);

// Helper functions
export function getUserDisplayName(user: UserProfile | null): string {
  if (!user) return 'Guest';
  
  if (user.first_name || user.last_name) {
    return `${user.first_name} ${user.last_name}`.trim();
  }
  
  return user.username;
}

export function getUserInitials(user: UserProfile | null): string {
  if (!user) return 'G';
  
  if (user.first_name || user.last_name) {
    const firstInitial = user.first_name ? user.first_name[0] : '';
    const lastInitial = user.last_name ? user.last_name[0] : '';
    return (firstInitial + lastInitial).toUpperCase();
  }
  
  return user.username.substring(0, 2).toUpperCase();
}
