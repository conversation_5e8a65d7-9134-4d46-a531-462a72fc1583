/**
 * Centralized error handling utilities
 */

import { addBreadcrumb } from '$lib/sentry';

// Error types for better categorization
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Enhanced API Error interface
export interface ApiError extends Error {
  type: ErrorType;
  severity: ErrorSeverity;
  status?: number;
  code?: string;
  data?: any;
  response?: Response;
  timestamp: Date;
  userMessage: string; // User-friendly message
  technicalMessage: string; // Technical details for debugging
  retryable: boolean;
  context?: Record<string, any>;
}

// User-friendly error messages
const ERROR_MESSAGES: Record<number, string> = {
  400: 'The request contains invalid data. Please check your input and try again.',
  401: 'Your session has expired. Please log in again.',
  403: 'You do not have permission to perform this action.',
  404: 'The requested resource was not found.',
  409: 'This action conflicts with existing data. Please refresh and try again.',
  422: 'The data provided is invalid. Please check the form and try again.',
  429: 'Too many requests. Please wait a moment and try again.',
  500: 'A server error occurred. Please try again later.',
  502: 'The service is temporarily unavailable. Please try again later.',
  503: 'The service is temporarily unavailable. Please try again later.',
  504: 'The request timed out. Please try again.',
};

// Default user-friendly message
const DEFAULT_ERROR_MESSAGE = 'An unexpected error occurred. Please try again.';

/**
 * Determines error type based on status code
 */
function getErrorType(status?: number): ErrorType {
  if (!status) return ErrorType.NETWORK;
  
  if (status === 401) return ErrorType.AUTHENTICATION;
  if (status === 403) return ErrorType.AUTHORIZATION;
  if (status >= 400 && status < 500) return ErrorType.CLIENT;
  if (status >= 500) return ErrorType.SERVER;
  
  return ErrorType.UNKNOWN;
}

/**
 * Determines error severity based on status code and type
 */
function getErrorSeverity(status?: number, type?: ErrorType): ErrorSeverity {
  if (!status) return ErrorSeverity.MEDIUM;
  
  if (status === 401 || status === 403) return ErrorSeverity.HIGH;
  if (status >= 500) return ErrorSeverity.HIGH;
  if (status === 429) return ErrorSeverity.MEDIUM;
  if (status >= 400 && status < 500) return ErrorSeverity.LOW;
  
  return ErrorSeverity.MEDIUM;
}

/**
 * Determines if an error is retryable
 */
function isRetryable(status?: number, type?: ErrorType): boolean {
  if (!status) return true; // Network errors are retryable
  
  // Server errors and rate limiting are retryable
  if (status >= 500 || status === 429) return true;
  
  // Client errors are generally not retryable
  if (status >= 400 && status < 500) return false;
  
  return false;
}

/**
 * Creates an enhanced API error with proper categorization
 */
export function createApiError(
  message: string,
  status?: number,
  response?: Response,
  data?: any,
  context?: Record<string, any>
): ApiError {
  const type = getErrorType(status);
  const severity = getErrorSeverity(status, type);
  const retryable = isRetryable(status, type);
  
  const userMessage = status ? (ERROR_MESSAGES[status] || DEFAULT_ERROR_MESSAGE) : DEFAULT_ERROR_MESSAGE;
  const technicalMessage = message || 'Unknown error occurred';
  
  const error = new Error(userMessage) as ApiError;
  
  error.type = type;
  error.severity = severity;
  error.status = status;
  error.data = data;
  error.response = response;
  error.timestamp = new Date();
  error.userMessage = userMessage;
  error.technicalMessage = technicalMessage;
  error.retryable = retryable;
  error.context = context;
  
  // Extract error code from response data if available
  if (data && typeof data === 'object') {
    error.code = data.code || data.error_code || data.type;
  }
  
  return error;
}

/**
 * Logs error with appropriate level and context
 */
export function logError(error: ApiError, additionalContext?: Record<string, any>) {
  const logContext = {
    type: error.type,
    severity: error.severity,
    status: error.status,
    code: error.code,
    retryable: error.retryable,
    timestamp: error.timestamp.toISOString(),
    ...error.context,
    ...additionalContext,
  };
  
  // Add breadcrumb for error tracking
  addBreadcrumb(`API Error: ${error.technicalMessage}`, 'error', {
    status: error.status,
    type: error.type,
    severity: error.severity,
  });
  
  // Log based on severity
  switch (error.severity) {
    case ErrorSeverity.CRITICAL:
    case ErrorSeverity.HIGH:
      console.error('API Error:', error.technicalMessage, logContext);
      break;
    case ErrorSeverity.MEDIUM:
      console.warn('API Warning:', error.technicalMessage, logContext);
      break;
    case ErrorSeverity.LOW:
      console.info('API Info:', error.technicalMessage, logContext);
      break;
    default:
      console.log('API Log:', error.technicalMessage, logContext);
  }
}

/**
 * Handles API errors with proper logging and user notification
 */
export function handleApiError(
  error: any,
  context?: Record<string, any>
): ApiError {
  let apiError: ApiError;
  
  if (error instanceof Error && 'type' in error) {
    // Already an ApiError
    apiError = error as ApiError;
  } else if (error instanceof Error) {
    // Convert regular Error to ApiError
    apiError = createApiError(
      error.message,
      (error as any).status,
      (error as any).response,
      (error as any).data,
      context
    );
  } else {
    // Handle non-Error objects
    apiError = createApiError(
      'Unknown error occurred',
      undefined,
      undefined,
      error,
      context
    );
  }
  
  // Log the error
  logError(apiError, context);
  
  return apiError;
}

/**
 * Creates a user-friendly error message for display
 */
export function getDisplayMessage(error: ApiError): string {
  return error.userMessage;
}

/**
 * Checks if an error should trigger a retry
 */
export function shouldRetry(error: ApiError, retryCount: number = 0, maxRetries: number = 3): boolean {
  return error.retryable && retryCount < maxRetries;
}

/**
 * Calculates retry delay with exponential backoff
 */
export function getRetryDelay(retryCount: number, baseDelay: number = 1000): number {
  return Math.min(baseDelay * Math.pow(2, retryCount), 10000); // Max 10 seconds
}
