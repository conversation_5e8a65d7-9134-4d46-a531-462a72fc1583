{"name": "svelte-latest", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.14", "@types/node": "^22.15.17", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-preprocess": "^6.0.3", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "vite": "^6.2.6"}, "dependencies": {"@sentry/svelte": "^9.39.0", "@sentry/vite-plugin": "^3.6.0"}}