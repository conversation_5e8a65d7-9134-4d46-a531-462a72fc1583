/**
 * Integration test script to verify frontend-backend connectivity
 */

const API_BASE_URL = 'http://localhost:8000';

async function testApiConnection() {
  console.log('🔍 Testing API connection...');
  
  try {
    // Test 1: API Root endpoint
    console.log('\n1. Testing API root endpoint...');
    const rootResponse = await fetch(`${API_BASE_URL}/api/v1/`);
    const rootData = await rootResponse.json();
    console.log('✅ API Root:', rootData);
    
    // Test 2: Login
    console.log('\n2. Testing login...');
    const loginResponse = await fetch(`${API_BASE_URL}/api/v1/auth/login/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });
    
    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }
    
    const loginData = await loginResponse.json();
    console.log('✅ Login successful:', {
      message: loginData.message,
      user: loginData.user.username,
      hasToken: !!loginData.access_token
    });
    
    const token = loginData.access_token;
    
    // Test 3: Authenticated request
    console.log('\n3. Testing authenticated request...');
    const meResponse = await fetch(`${API_BASE_URL}/api/v1/auth/me/`, {
      headers: {
        'Authorization': `Token ${token}`
      }
    });
    
    if (!meResponse.ok) {
      throw new Error(`Me endpoint failed: ${meResponse.status}`);
    }
    
    const meData = await meResponse.json();
    console.log('✅ User profile:', {
      username: meData.username,
      email: meData.email,
      hasProfile: !!meData.profile
    });
    
    // Test 4: Create a todo
    console.log('\n4. Testing todo creation...');
    const todoResponse = await fetch(`${API_BASE_URL}/api/v1/todo/todos/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: 'Integration Test Todo',
        description: 'Testing frontend-backend integration',
        priority: 'medium'
      })
    });
    
    if (!todoResponse.ok) {
      throw new Error(`Todo creation failed: ${todoResponse.status}`);
    }
    
    const todoData = await todoResponse.json();
    console.log('✅ Todo created:', {
      id: todoData.id,
      title: todoData.title,
      status: todoData.status
    });
    
    // Test 5: Get todos
    console.log('\n5. Testing todo list...');
    const todosResponse = await fetch(`${API_BASE_URL}/api/v1/todo/todos/`, {
      headers: {
        'Authorization': `Token ${token}`
      }
    });
    
    if (!todosResponse.ok) {
      throw new Error(`Todo list failed: ${todosResponse.status}`);
    }
    
    const todosData = await todosResponse.json();
    console.log('✅ Todo list:', {
      count: todosData.length,
      todos: todosData.map(t => ({ id: t.id, title: t.title }))
    });
    
    // Test 6: Logout
    console.log('\n6. Testing logout...');
    const logoutResponse = await fetch(`${API_BASE_URL}/api/v1/auth/logout/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!logoutResponse.ok) {
      throw new Error(`Logout failed: ${logoutResponse.status}`);
    }
    
    const logoutData = await logoutResponse.json();
    console.log('✅ Logout successful:', logoutData);
    
    console.log('\n🎉 All integration tests passed!');
    console.log('\n📋 Summary:');
    console.log('- ✅ API connectivity working');
    console.log('- ✅ Authentication working (Token-based)');
    console.log('- ✅ CRUD operations working');
    console.log('- ✅ User session management working');
    console.log('\n🚀 Frontend can now safely connect to Django backend!');
    
  } catch (error) {
    console.error('\n❌ Integration test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testApiConnection();
